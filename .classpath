<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="src" path="Test"/>
	<classpathentry kind="src" path="Resource"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/coverage"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/draw"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/junit"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/log"/>
	<classpathentry kind="lib" path="libs/3d/jmathplot.jar"/>
	<classpathentry kind="lib" path="libs/asm-all-5.0.4.jar"/>
	<classpathentry kind="lib" path="libs/gral-core-0.11.jar"/>
	<classpathentry kind="lib" path="libs/jheatchart-0.6.jar"/>
	<classpathentry kind="lib" path="libs/junit-4.5.jar"/>
	<classpathentry kind="lib" path="libs/log4j-api-2.5-sources.jar"/>
	<classpathentry kind="lib" path="libs/log4j-api-2.5.jar"/>
	<classpathentry kind="lib" path="libs/log4j-core-2.5.jar"/>
	<classpathentry kind="lib" path="libs/org.jacoco.core-0.7.6.201602180812.jar" sourcepath="C:/Users/<USER>/Desktop/jacoco-0.7.6.zip"/>
	<classpathentry kind="lib" path="libs/org.jacoco.report-0.7.6.201602180812.jar"/>
	<classpathentry kind="lib" path="/ART_Heuristic/libs/poi-3.16.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
