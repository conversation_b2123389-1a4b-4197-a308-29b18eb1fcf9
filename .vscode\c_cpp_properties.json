{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "C:/Program Files/RedHat/java-1.8.0-openjdk-1.8.0.452-1/include", "C:/Program Files/RedHat/java-1.8.0-openjdk-1.8.0.452-1/include/win32"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "_USRDLL", "_WINDLL"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64"}], "version": 4}