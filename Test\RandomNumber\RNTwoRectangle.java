package RandomNumber;

import java.util.Random;

import datastructure.TD.TestCase;
import util.draw.StdDraw;

class Rectangle {
	TestCase p1;
	TestCase p2;

	public Rectangle() {
		super();
	}

	public Rectangle(TestCase p1, TestCase p2) {
		super();
		this.p1 = p1;
		this.p2 = p2;
	}

}

public class RNTwoRectangle {
	public static void main(String[] args) {
		Rectangle r1 = new Rectangle();
		r1.p1 = new TestCase(0.2, 0.2);
		r1.p2 = new TestCase(0.4, 0.4);
		Rectangle r2 = new Rectangle();
		r2.p1 = new TestCase(0.4, 0.2);
		r2.p2 = new TestCase(0.6, 0.6);
		StdDraw.rectangle(0.3, 0.3, 0.1, 0.1);
		StdDraw.rectangle(0.5, 0.4, 0.1, 0.2);

		Random random = new Random(3);
		for (int i = 0; i < 10000; i++) {
			double temp = random.nextDouble();
			TestCase p = new TestCase();
			if (temp < 1.0 / 3.0) {
				p.p = random.nextDouble() * (0.2) + 0.2;
				p.q = random.nextDouble() * (0.2) + 0.2;
			} else {
				p.p = random.nextDouble() * (0.2) + 0.4;
				p.q = random.nextDouble() * (0.4) + 0.2;
			}
			StdDraw.point(p.p, p.q);
		}
	}
}
