package RandomNumber;

import util.draw.StdDraw;

public class RandomNumberInCircle {

	public static void main(String[] args) {
		double r = 0.5;
		int num = 5000;
		StdDraw.circle(0.5, 0.5, 0.5);
		// tu[0] = ParametricPlot[{r*Sin[u], r*Cos[u]}, {u, 0, 2 Pi}];
		for (int i = 1; i <= num; i++) {
			double theta = Math.random() * 2 * Math.PI;
			double rr = Math.random() * (r * r);
			double x = Math.sin(theta) * (Math.sqrt(rr));
			double y = Math.cos(theta) * (Math.sqrt(rr));

			double theta2 = Math.random() * Math.PI * 0.25;
			double r2 = Math.random() * (Math.pow(r * (1 / Math.cos(theta2)), 3) - Math.pow(r, 3)) + Math.pow(r, 3);
			double x2 = Math.cos(theta2) * Math.pow(r2, 1.0 / 3.0);
			double y2 = Math.sin(theta2) * Math.pow(r2, 1.0 / 3.0);

			StdDraw.filledCircle(x2 + 0.5, y2 + 0.5, 0.001);
		}

	}
}
