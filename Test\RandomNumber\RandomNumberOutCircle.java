package RandomNumber;

import util.draw.StdDraw;

public class RandomNumberOutCircle {

	public static void main(String[] args) {
		double r = 0.5;
		int num = 50000;
		StdDraw.circle(0.5, 0.5, 0.5);
		for (int i = 1; i <= num; i++) {
			double a = Math.random();
			double w = a * 2 * Math.PI;

			double s = 1;
			double xi = s * 0.5 / Math.tan(w);
			double b = Math.random();
			double R = Math.sqrt(xi * xi + (s * 0.5) * (s * 0.5));
			double rho = r + b * R;
			double X = rho * Math.cos(w);
			double Y = rho * Math.sin(w);
			StdDraw.filledCircle(X + 0.5, Y + 0.5, 0.001);
		}
		System.out.println("done");
	}
}
