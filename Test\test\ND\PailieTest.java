package test.ND;

import java.util.ArrayList;
import java.util.Arrays;

import util.PaiLie;

public class PailieTest {
	public static ArrayList<double[]> GetAll(double[] xn, double[] yn) {
		double b[][] = new double[xn.length][2];
		for (int i = 0; i < xn.length; i++) {
			b[i] = new double[] { xn[i], yn[i] };
		}

		double a[][] = b;
		String s;
		// 总循环次数，控制循环量
		int it = (int) Math.pow((a[0].length), (a.length)) - 1;
		ArrayList<double[]> list = new ArrayList<>();
		while (it >= 0) {
			s = "";
			// it % a[0].length;
			// (it / a[0].length) % a[0].length;
			// (it / a[0].length) / a[0].length % a[0].length;
			// 临时变量，保存迭代器
			int temp = it;
			for (int m = 0; m < a.length; m++) {
				if (temp / a[0].length >= 0) {
					s += a[m][temp % a[0].length] + " ";
					temp /= a[0].length;
				}
			}
			String[] tempStr = s.split(" ");
			double[] tempVal = new double[tempStr.length];
			for (int i = 0; i < tempStr.length; i++) {
				tempVal[i] = Double.parseDouble(tempStr[i]);
			}
			list.add(tempVal);
			// System.out.println(s);
			it--;
		}
		return list;
	}

	public static void main(String[] args) {
		double[] xn = { 1.1, 2.1 };
		double[] yn = { 2.2, 3.2 };

		ArrayList<double[]> lists = PaiLie.GetAll(xn, yn);
		System.out.println("count:" + lists.size());
		for (int i = 0; i < lists.size(); i++) {
			System.out.println(Arrays.toString(lists.get(i)));
		}
		// double p
		double[] p = { 1.8, 2.9 };
		double min[] = new double[p.length];
		double max[] = new double[p.length];
		for (int i = 0; i < lists.size(); i++) {
			double q[] = lists.get(i);
			for (int j = 0; j < p.length; j++) {
				if (p[j] < q[j]) {
					min[j] = p[j];
					max[j] = q[j];
				} else {
					max[j] = p[j];
					min[j] = q[j];
				}

			}
			System.out.println(Arrays.toString(min) + "," + Arrays.toString(max));
		}

	}
}
