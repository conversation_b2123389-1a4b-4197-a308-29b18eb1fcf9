package test.ND;

import javax.swing.JFrame;

import org.math.plot.Plot3DPanel;

public class Plot3DTest {
	static double PI = Math.PI;

	// function definition: z=cos(PI*x)*sin(PI*y)
	public static double f1(double x, double y) {
		double z = x + y;
		return z;
	}

	// grid version of the function
	public static double[][] f1(double[] x, double[] y) {
		double[][] z = new double[y.length][x.length];
		for (int i = 0; i < x.length; i++)
			for (int j = 0; j < y.length; j++)
				z[j][i] = f1(x[i], y[j]);
		return z;
	}

	// another function definition: z=sin(PI*x)*cos(PI*y)
	public static double f2(double x, double y) {
		double z = Math.sin(x * PI) * Math.cos(y * PI);
		return z;
	}

	// grid version of the function
	public static double[][] f2(double[] x, double[] y) {
		double[][] z = new double[y.length][x.length];
		for (int i = 0; i < x.length; i++)
			for (int j = 0; j < y.length; j++)
				z[j][i] = f2(x[i], y[j]);
		return z;
	}

	public static double[] increment(double start, double step, double end) {
		int count = (int) ((end - start) / step);
		double[] results = new double[count];
		for (int i = 0; i < results.length; i++) {
			results[i] = start + i * step;
		}
		return results;
	}

	// JavaPlot p = new JavaPlot("F:\\Program
	public static void main(String[] args) {

		// define your data
		double[] x = increment(0.0, 0.1, 1.0); // x = 0.0:0.1:1.0
		double[] y = increment(0.0, 0.05, 1.0);// y = 0.0:0.05:1.0
		double[][] z1 = f1(x, y);
		double[][] z2 = f2(x, y);

		// create your PlotPanel (you can use it as a JPanel) with a legend at SOUTH
		Plot3DPanel plot = new Plot3DPanel("SOUTH");

		// add grid plot to the PlotPanel
		plot.addGridPlot("z=cos(PI*x)*sin(PI*y)", x, y, z1);
		// plot.addGridPlot("z=sin(PI*x)*cos(PI*y)", x, y, z2);

		// put the PlotPanel in a JFrame like a JPanel
		JFrame frame = new JFrame("a plot panel");
		frame.setSize(600, 600);
		frame.setContentPane(plot);
		frame.setVisible(true);
		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
	}
}
