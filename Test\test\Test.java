package test;

import java.util.ArrayList;

import datastructure.ND.NPoint;
import datastructure.ND.TPInfo;

public class Test {
	public static void addFour(ArrayList<Integer> list) {
		list.add(31);
		list.add(32);
		list.add(33);
		list.add(34);
	}

	public static void main(String[] args) {
		double[] pn = { 0.2, 0.5, 0.8 };
		double[] min = { 0, 0, 0 };
		double[] max = { 1, 1, 1 };
		double[] xn = new double[8];

		TPInfo info1 = new TPInfo();
		info1.start = new NPoint(new double[] { min[0], min[1], min[2] });
		info1.end = new NPoint(new double[] { pn[0], pn[1], pn[2] });

		TPInfo info2 = new TPInfo();
		info2.start = new NPoint(new double[] { min[0], min[1], pn[2] });
		info2.end = new NPoint(new double[] { pn[0], pn[1], max[2] });

		TPInfo info3 = new TPInfo();
		info3.start = new NPoint(new double[] { min[0], pn[1], min[2] });
		info3.end = new NPoint(new double[] { pn[0], max[1], pn[2] });

		TPInfo info4 = new TPInfo();
		info4.start = new NPoint(new double[] { min[0], pn[1], pn[2] });
		info4.end = new NPoint(new double[] { pn[0], max[1], max[2] });

		TPInfo info5 = new TPInfo();
		info5.start = new NPoint(new double[] { pn[0], min[1], min[2] });
		info5.end = new NPoint(new double[] { max[0], pn[1], pn[2] });

		TPInfo info6 = new TPInfo();
		info6.start = new NPoint(new double[] { pn[0], min[1], pn[2] });
		info6.end = new NPoint(new double[] { max[0], pn[1], max[2] });

		TPInfo info7 = new TPInfo();
		info7.start = new NPoint(new double[] { pn[0], min[1], pn[2] });
		info7.end = new NPoint(new double[] { max[0], pn[1], max[2] });

		TPInfo info8 = new TPInfo();
		info8.start = new NPoint(new double[] { pn[0], pn[1], pn[2] });
		info8.end = new NPoint(new double[] { max[0], max[1], max[2] });

		System.out.println();
	}

	public static int randomInATPInfo() {
		double T = 0.8;
		// System.out.println("C:" + C);
		System.out.println("T:" + T);
		double SumIntegral = 0.0;// 积分值总和
		// int temp = 0;// 落在哪个区间
		int temp = 0;
		double[] regions = { 0.2, 0.5, 0.5, 0.3 };
		for (int i = 0; i < regions.length; i++) {
			if (SumIntegral < T) {
				temp = i;
			}
			SumIntegral += regions[i];
		}
		System.out.println("index:" + temp);
		return temp;
	}
}
