package test;

import java.util.ArrayList;

public class Test2 {
	static int count = 0;

	public static void main(String[] args) {
		double[] start = { 0, 0, 0 };
		double[] xn = { 0.2, 0.5, 0.8 };

		Test2.perm(0, start, xn, new ArrayList<>());

		System.out.println("总组合数" + count);
	}

	public static void perm(int i, double[] start, double[] xn, ArrayList<Double> list) {
		if (i == 2) {
			for (int k = 0; k < list.size(); k++) {
				System.out.print(list.get(k));
			}
			System.out.println();
		}

		ArrayList list1 = new ArrayList<>();
		list1.add(start[i]);
		perm(i + 1, start, xn, list1);
		ArrayList list2 = new ArrayList<>();
		list2.add(xn[i]);
		perm(i + 1, start, xn, list2);

	}
}