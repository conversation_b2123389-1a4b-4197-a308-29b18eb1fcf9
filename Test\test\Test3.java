package test;

import java.util.ArrayList;
import java.util.List;

public class Test3 {
	static int count = 0;
	static ArrayList<double[]> lists = new ArrayList<>();

	public static void main(String[] args) {
		// lists.add(choice1);
		// lists.add(choice2);
		// lists.add(choice3);

		double[] start = { 0, 0, 0 };
		double[] xn = { 0.2, 0.5, 0.8 };
		for (int i = 0; i < start.length; i++) {
			lists.add(new double[] { start[i], xn[i] });
		}
		Test3.perm(lists.size(), 0, new ArrayList<Double>());
		System.out.println("总组合数" + count);
	}

	public static void perm(int n, int k, List<Double> list) {
		if (list.size() == n) {
			for (int l = 0; l < list.size(); l++) {
				System.out.print(list.get(l) + ",");
			}
			count++;
			System.out.println();
		} else {
			for (int i = 0; i < 2; i++) {
				List<Double> list2 = new ArrayList<Double>(list);
				list2.add(lists.get(k)[i]);
				perm(n, ++k, list2);
				k--;
			}
		}
	}
}
