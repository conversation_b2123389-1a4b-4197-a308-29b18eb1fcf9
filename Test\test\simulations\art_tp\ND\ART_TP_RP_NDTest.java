package test.simulations.art_tp.ND;

import java.util.ArrayList;

import org.junit.Test;

import datastructure.ND.NPoint;

public class ART_TP_RP_NDTest {

	@Test
	public void testMinAllTC() {
		int dimension = 3;
		ArrayList<NPoint> tests = new ArrayList<>();
		tests.add(new NPoint(new double[] { 0.3, 0.4, 0.5 }));
		tests.add(new NPoint(new double[] { 0.2, 0.6, 0.2 }));
		tests.add(new NPoint(new double[] { 0.1, 0.5, 0.6 }));
		double[][] results = new double[tests.size()][dimension];
		// 得到每一列的值
		for (int i = 0; i < tests.size(); i++) {
			for (int j = 0; j < tests.get(i).getXn().length; j++) {
				results[i][j] = tests.get(i).getXn()[j];
			}
		}
		double[] max = results[0];
		for (int i = 0; i < results.length; i++) {
			for (int j = 0; j < results[i].length; j++) {
				if (max[j] < results[i][j]) {
					max[j] = results[i][j];
				}
			}
		}

		for (int i = 0; i < max.length; i++) {
			System.out.println(max[i]);
		}
	}

}
