package test.simulations.art_tp._2D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;

/*
 * 测试
 * 生成二维的ART_TP，用来探索n维的测试用例
 * 其中使用二维特殊化的，均有二维特殊化提醒
 * */
public class Solution {
	double[] min;
	double[] max;
	double fail_rate;
	double[] fail_start;
	long seed;
	int dimension;
	double totalAreaS;
	double failAreaS;
	double eachFailLength;
	Random random;
	ArrayList<NPoint> tests = new ArrayList<>();
	ArrayList<NRectRegion> regions = new ArrayList<>();
	double C;// 常数

	public Solution(double[] min, double[] max, double fail_rate, long seed) {
		this.min = min;
		this.max = max;
		this.fail_rate = fail_rate;
		this.seed = seed;
		random = new Random(this.seed);
		if (min.length == max.length) {
			this.dimension = min.length;
		}
		totalAreaS = 1.0;
		for (int i = 0; i < this.dimension; i++) {
			totalAreaS *= max[i] - min[i];
		}
		this.failAreaS = fail_rate * totalAreaS;
		this.eachFailLength = Math.pow(failAreaS, 1.0 / (double) this.dimension);
		// generate Fail start;
		for (int i = 0; i < this.dimension; i++) {
			fail_start[i] = random.nextDouble() * (max[i] - min[i] - this.eachFailLength) + min[i];
		}
	}

	public void addTDRegions(double[] min, double[] max, NPoint p) {
		NRectRegion region1 = new NRectRegion();
		region1.setStart(new NPoint(new double[] { min[0], min[1] }));
		region1.setEnd(new NPoint(p.getXn()));

		NRectRegion region2 = new NRectRegion();
		region2.setStart(new NPoint(new double[] { p.getXn()[0], min[1] }));
		region2.setEnd(new NPoint(new double[] { max[0], p.getXn()[1] }));

		NRectRegion region3 = new NRectRegion();
		region3.setStart(new NPoint(p.getXn()));
		region3.setEnd(new NPoint(new double[] { max[0], max[1] }));

		NRectRegion region4 = new NRectRegion();
		region4.setStart(new NPoint(new double[] { min[0], p.getXn()[1] }));
		region4.setEnd(new NPoint(new double[] { p.getXn()[0], max[1] }));

		regions.add(region1);
		regions.add(region2);
		regions.add(region3);
		regions.add(region4);
	}

	public double calEachIntEC(double u, double v, double f, double t) {
		// int((x-u)*(v-x),f,t)
		return -(1.0 / 3.0) * (t * t * t - f * f * f) + (0.5 * (u + v) * (t * t - f * f)) - u * v * (t - f);
	}

	public double calEachRegion(ArrayList<Double> list) {
		double tempC = 1.0;
		for (int i = 0; i < regions.size(); i++) {
			// 二维特殊化
			NRectRegion temp = regions.get(i);
			NPoint start = temp.getStart();
			NPoint end = temp.getEnd();
			double from1 = 0.0;
			double to1 = 0.0;
			double from2 = 0.0;
			double to2 = 0.0;
			if (start.getXn()[0] == min[0]) {
				from1 = random.nextDouble() * (end.getXn()[0] - min[0]) + (2 * min[0] - end.getXn()[0]);
			} else {
				from1 = start.getXn()[0];
			}
			if (start.getXn()[1] == min[1]) {
				from2 = random.nextDouble() * (end.getXn()[1] - min[1]) + (2 * min[1] - end.getXn()[1]);
			} else {
				from2 = start.getXn()[1];
			}
			if (end.getXn()[0] == max[0]) {
				to1 = random.nextDouble() * (max[0] - end.getXn()[0]) + max[0];
			} else {
				to1 = end.getXn()[0];
			}
			if (end.getXn()[1] == max[1]) {
				to2 = random.nextDouble() * (max[1] - end.getXn()[1]) + max[1];
			} else {
				to2 = end.getXn()[1];
			}
			double a = calEachIntEC(start.getXn()[0], end.getXn()[0], from1, to1);
			double b = calEachIntEC(start.getXn()[1], end.getXn()[1], from2, to2);
			tempC *= (a * b);
			list.add(a * b);
		}
		return tempC;
	}

	public boolean isCorrect(NPoint p) {
		// boolean flag=true;
		double[] xn = p.getXn();
		boolean lead2Fail = false;
		for (int i = 0; i < this.dimension; i++) {
			if (xn[i] < this.fail_start[i] || xn[i] > (this.fail_start[i] + this.eachFailLength)) {
				lead2Fail = true;
			}
		}
		// System.out.println(Arrays.toString(nDPoints));
		// System.out.println("isFail:"+lead2Fail);
		// lead2Fail=false,失效，=true不失效
		return lead2Fail;
	}

	public boolean isInRegion(NRectRegion region, NPoint p) {
		boolean flag = true;
		double[] pxn = p.getXn();
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		for (int i = 0; i < this.dimension; i++) {
			if (pxn[i] < start[i] || pxn[i] > end[i]) {
				flag = false;
			}
		}
		return flag;
	}

	public NPoint randomTC() {
		// generate from the input domain
		NPoint point = new NPoint();
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setDimension(this.dimension);
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		// generate a new test case random
		NPoint p = randomTC();
		while (isCorrect(p)) {
			count++;
			tests.add(p);
			updateRegions(p);// 重新计算现有的区域
			// generate next test case by test profile
			ArrayList<Double> eachRegionProbility = new ArrayList<>(this.regions.size());
			C = calEachRegion(eachRegionProbility);
			C = 1.0 / C;
			// 随机产生一个0-1的数
			double T = random.nextDouble();
			double SumIntegral = 0.0;// 积分值总和
			double PreIntegral = 0.0;
			int temp = 0;// 落在哪个区间
			for (int i = 0; i < eachRegionProbility.size(); i++) {
				if (SumIntegral < T) {
					PreIntegral = SumIntegral;
					temp = i;
				}
				SumIntegral += eachRegionProbility.get(i) * C;
			}
			// 计算积分值Pre+int((C)*(x1-from1)*(to1-x1)*(x2-from2)*(to2-x2))
			// 二维特殊化
			double[] start = this.regions.get(temp).getStart().getXn();
			double[] end = this.regions.get(temp).getEnd().getXn();

		}
		return count;
	}

	public void updateRegions(NPoint p) {
		if (regions.size() == 0) {
			// 初始区域，添加四块区域（2的m次快）
			int regionsCount = (int) Math.pow(2, this.dimension);
			// 这里针对二维的
			addTDRegions(this.min, this.max, p);
			/// 二维特殊化
		} else {
			for (int i = 0; i < regions.size(); i++) {
				// 先判断这个点在不在这个区域内,要先删除区域
				if (isInRegion(regions.get(i), p)) {
					NRectRegion region = regions.remove(i);
					i--;
					addTDRegions(region.getStart().getXn(), region.getEnd().getXn(), p);
					/// 二维特殊化
					break;
				}
			}
		}
	}

}
