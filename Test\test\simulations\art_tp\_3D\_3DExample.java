package test.simulations.art_tp._3D;

import java.awt.Color;
import java.util.Arrays;

import javax.swing.JFrame;

import org.math.plot.Plot3DPanel;

public class _3DExample {
	static double u1 = 0.2;
	static double v1 = 0.5;

	static double u2 = 0.5;
	static double v2 = 0.8;

	static double u3 = 0.8;
	static double v3 = 1.0;
	static double C;

	public static double calC() {
		C = 1.0 / (calEC(u1, v1) * calEC(u2, v2) * calEC(u3, v3));
		return C;
	}

	public static double calEC(double u, double v) {
		// int((x-u)*(v-x),u,v)
		return -(1.0 / 3.0) * (v * v * v - u * u * u) + (0.5 * (u + v) * (v * v - u * u)) - u * v * (v - u);
	}

	public static double genNext(double fixu1, double fixv1, double fixu2, double fixv2, double u, double v) {
		double next = 0.0;
		double Co = C * calEC(fixu1, fixv1) * calEC(fixu2, fixv2);
		double T = Math.random();
		// System.out.println("T:" + T);
		double A = -(1.0 / 3.0) * Co;
		double B = 0.5 * (u + v) * Co;
		double C = -u * v * Co;
		double D = -Co * (-(1.0 / 3.0) * u * u * u + 0.5 * (u + v) * u * u - u * v * u) - T;
		double arrays[] = shengjinFormula(A, B, C, D);
		boolean flag = false;
		for (int i = 0; i < arrays.length; i++) {
			if (arrays[i] > u && arrays[i] < v) {
				flag = true;
				next = arrays[i];
			}

		}
		if (!flag) {
			System.out.println("error");
		}
		// System.out.println();
		return next;
	}

	public static void main(String[] args) {
		calC();
		int times = 8000;
		double[] valuesX = new double[times];
		double[] valuesY = new double[times];
		double[] valuesZ = new double[times];
		for (int i = 0; i < times; i++) {
			valuesX[i] = genNext(u2, v2, u3, v3, u1, v1);
			valuesY[i] = genNext(u1, v1, u3, v3, u2, v2);
			valuesZ[i] = genNext(u1, v1, u2, v2, u3, v3);
		}
		System.out.println(Arrays.toString(valuesX));
		System.out.println(Arrays.toString(valuesY));
		System.out.println(Arrays.toString(valuesZ));
		Plot3DPanel plot = new Plot3DPanel("SOUTH");

		// add grid plot to the PlotPanel
		// plot.addGridPlot("z=cos(PI*x)*sin(PI*y)", valuesX, valuesY, z);
		plot.addScatterPlot("test", Color.red, valuesX, valuesY, valuesZ);
		JFrame frame = new JFrame("a plot panel");
		frame.setSize(600, 600);
		frame.setContentPane(plot);
		frame.setVisible(true);
		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		// double z[]=new double[valuesX.length];
		// for (int i = 0; i < valuesX.length; i++) {
		// z[i]=C*(valuesX[i]-u1)*(v1-valuesX[i])*(valuesY[i]-u2)*(v2-valuesY[i]);
		// }
	}

	public static double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac
		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad
		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd
		double delta = B * B - 4.0 * A * C;
		double root = 0.0;
		double r1 = 0.0;
		double r2 = 0.0;
		double[] roots = new double[3];
		if (delta > 0) {
			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;
			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;
			double powY1;
			double powY2;
			if (Y1 < 0) {
				powY1 = -Math.pow(-Y1, 1.0 / 3.0);
			} else {
				powY1 = Math.pow(Y1, 1.0 / 3.0);
			}
			if (Y2 < 0) {
				powY2 = -Math.pow(-Y2, 1.0 / 3.0);
			} else {
				powY2 = Math.pow(Y2, 1.0 / 3.0);
			}
			root = (-bcof - powY1 - powY2) / (3.0 * acof);
			r1 = root;
			r2 = root;
		} else if (delta == 0) {
			root = -bcof / acof + B / A;
			r1 = -B / (2.0 * A);
			r2 = r1;

		} else if (delta < 0) {
			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));
			double theta = Math.acos(T);
			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);
			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))
					/ (3.0 * acof);
			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))
					/ (3.0 * acof);
		}
		roots[0] = root;
		roots[1] = r1;
		roots[2] = r2;
		return roots;
	}

	public static double test(double fixu, double fixv, double u, double v) {
		double Co = C * calEC(fixu, fixv);
		return Co * calEC(u, v);
	}
}
