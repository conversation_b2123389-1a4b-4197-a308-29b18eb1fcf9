@echo off
REM Build script for programlib.dll

REM Set Java paths based on the error message we saw
set JAVA_HOME=C:\Program Files\RedHat\java-1.8.0-openjdk-1.8.0.452-1
set JAVA_INCLUDE=%JAVA_HOME%\include
set JAV<PERSON>_INCLUDE_WIN32=%JAVA_HOME%\include\win32

REM Check if Java directories exist
if not exist "%JAVA_INCLUDE%" (
    echo Error: Java include directory not found at %JAVA_INCLUDE%
    echo Please check your Java installation
    pause
    exit /b 1
)

if not exist "%JAVA_INCLUDE_WIN32%" (
    echo Error: Java win32 include directory not found at %JAVA_INCLUDE_WIN32%
    echo Please check your Java installation
    pause
    exit /b 1
)

echo Building programlib.dll...
echo Java include: %JAVA_INCLUDE%
echo Java win32 include: %JAVA_INCLUDE_WIN32%

REM Compile the DLL using Visual Studio compiler
cl /LD /Fe:programlib.dll programlib.cpp ^
   /I"%JAVA_INCLUDE%" ^
   /I"%JAVA_INCLUDE_WIN32%" ^
   /D_USRDLL ^
   /D_WINDLL ^
   /MT ^
   /link /DEF:programlib.def

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! programlib.dll created.
    echo Copying to Resource directory...
    copy programlib.dll Resource\
    echo.
    echo DLL is ready for testing.
) else (
    echo.
    echo Build failed! Please check the error messages above.
    pause
)

pause
