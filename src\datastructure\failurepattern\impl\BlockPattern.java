package datastructure.failurepattern.impl;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;

public class BlockPattern extends FailurePattern {
	private double eachFailLength;
	private double fail_regionS;
	private double[] fail_start;

	@Override
	public void genFailurePattern() {
		fail_start = new double[this.dimension];
		double totalArea = 1.0;
		for (int i = 0; i < this.dimension; i++) {
			totalArea *= (max[i] - min[i]);
		}
		this.fail_regionS = this.fail_rate * totalArea;
		this.eachFailLength = Math.pow(fail_regionS, 1 / (double) this.dimension);
		// System.out.println("EachFailArea:"+this.EachFailLength);
		for (int i = 0; i < this.dimension; i++) {
			fail_start[i] = random.nextDouble() * (max[i] - min[i] - eachFailLength) + min[i];
		}
	}

	@Override
	public boolean isCorrect(NPoint p) {
		double[] xn = p.getXn();
		boolean lead2Fail = false;
		for (int i = 0; i < this.dimension; i++) {
			if (xn[i] < this.fail_start[i] || xn[i] > (this.fail_start[i] + eachFailLength)) {
				lead2Fail = true;
			}
		}
		// System.out.println(Arrays.toString(nDPoints));
		// System.out.println("isFail:"+lead2Fail);
		// lead2Fail=false,失效，=true不失效
		return lead2Fail;
	}

}
