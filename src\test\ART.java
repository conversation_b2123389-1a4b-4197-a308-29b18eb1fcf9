package test;

import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import util.RandomCreator;

public abstract class ART {
	public double[] min;
	public double[] max;
	public int dimension;
	public static Random random;
	public FailurePattern failPattern;
	public double totalArea;
	public RandomCreator randomCreator;

	public ART(double[] min, double[] max, Random random, FailurePattern failurePattern) {
		this.min = min;
		this.max = max;

		this.dimension = min.length;
		this.random = random;

		failurePattern.min = min;
		failurePattern.max = max;
		failurePattern.dimension = dimension;
		failurePattern.random = random;
		failurePattern.genFailurePattern();
		this.failPattern = failurePattern;

		totalArea = 1.0;
		for (int i = 0; i < this.dimension; i++) {
			totalArea *= (max[i] - min[i]);
		}

		randomCreator = new RandomCreator(random, dimension, min, max);
	}

	public abstract int run();
}
