package test.reality.art_b;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;
import tested.*;
import Fault_10d.CalGCD;
import Fault_10d.NearestDistance;
import Fault_5d.CalDay;
import Fault_6d.Complex;
import Fault_6d.Triangle;
import Fault_8d.Line;
import Fault_8d.TwoLinesPos;
import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import tested.bessj;

public class ART_B_ND extends ART {
	public static void main(String[] args) {
		//erfcc beTested=new erfcc();
		//tanh beTested=new tanh();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		//Tcas beTested = new Tcas();
		//Select beTested = new Select();
		//Triangle betesed = new Triangle();
		//Line beTested = new Line();
		CalDay beTested = new CalDay();
		//CalGCD beTested = new CalGCD();
		//Complex beTested = new Complex();
		//TwoLinesPos beTested = new TwoLinesPos();
		//NearestDistance beTested = new NearestDistance();
		// 一定要修改这个
	
				int d = beTested.Dimension;
		// m 维立方体
		// ZeroOneCreator dataCreator = new ZeroOneCreator();
		double start[] = beTested.min;
		double end[] = beTested.max;
		int times = 3000;
		long sums = 0;
		// FailurePattern failurePattern = new BlockPattern();
		// FailurePattern failurePattern=new StripPatternIn2D();
		FailurePattern failurePattern = new RealityFailPattern(beTested.getClass().getSimpleName());
		failurePattern.fail_rate = beTested.failureRate;
		failurePattern.min = start;
		failurePattern.max = end;
		failurePattern.dimension = d;

		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {

			ART_B_ND art_b_nd = new ART_B_ND(start, end, new Random(i * 3), failurePattern);
			int fm = art_b_nd.run();
			sums += fm;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: " + sums / (double) times);
		System.out.println("Time: " + (endTime - startTime) / (double) times);
	}
	ArrayList<NRectRegion> untestedRegions = new ArrayList<>();
	ArrayList<NRectRegion> testedRegions = new ArrayList<>();

	ArrayList<NPoint> tests = new ArrayList<>();

	public ART_B_ND(double[] min, double[] max, Random random, FailurePattern failurePattern) {
		super(min, max, random, failurePattern);
	}

	public NRectRegion findRandomRegionAndDelete(ArrayList<NRectRegion> regions) {
		int T = random.nextInt(regions.size());
		return regions.remove(T);
	}

	public NPoint genNextP(NRectRegion region) {
		NPoint p = new NPoint();
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double xn[] = new double[dimension];

		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];
		}
		p.setXn(xn);
		return p;
	}

	public boolean hasPointInRegion(NRectRegion region) {
		boolean result = false;
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();

		for (int i = 0; i < tests.size(); i++) {
			double[] p = tests.get(i).getXn();
			boolean isPIn = true;
			for (int j = 0; j < p.length; j++) {
				if (p[j] < start[j] || p[j] > end[j]) {
					isPIn = false;
				}
			}
			if (isPIn) {
				result = true;
				break;
			}
		}
		return result;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public NPoint randomTC(NRectRegion region) {
		NPoint p = new NPoint();
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double xn[] = new double[dimension];

		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];
		}
		p.setXn(xn);
		return p;
	}

	@Override
	public int run() {
		int count = 0;
		untestedRegions.add(new NRectRegion(new NPoint(min), new NPoint(max)));

		while (true) {
			NPoint p = null;
			while (untestedRegions.size() != 0) {
				NRectRegion randomRegion = findRandomRegionAndDelete(untestedRegions);
				// System.out.println("randomRegion:"+randomRegion);
				p = randomTC(randomRegion);
				// System.out.println("point:"+p);
				count++;
				tests.add(p);
				if (!this.failPattern.isCorrect(p)) {
					return count;
				} else {
					testedRegions.add(randomRegion);
				}
			}
			ArrayList<NRectRegion> temp = new ArrayList<>();
			for (int i = 0; i < testedRegions.size(); i++) {
				// 找最长边
				NRectRegion tempRegion = testedRegions.get(i);
				double maxBian = 0.0;
				int maxIndex = 0;
				for (int j = 0; j < tempRegion.getStart().getXn().length; j++) {
					if (tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j] > maxBian) {
						maxBian = tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j];
						maxIndex = j;
					}
				}
				//
				NRectRegion region1 = new NRectRegion();
				NRectRegion region2 = new NRectRegion();

				region1.setStart(tempRegion.getStart());
				double[] end = Arrays.copyOf(tempRegion.getEnd().getXn(), tempRegion.getEnd().getXn().length);
				double midValue1 = 0.5
						* (tempRegion.getEnd().getXn()[maxIndex] + tempRegion.getStart().getXn()[maxIndex]);
				end[maxIndex] = midValue1;
				region1.setEnd(new NPoint(end));
				if (hasPointInRegion(region1)) {
					temp.add(region1);
				} else {
					untestedRegions.add(region1);
				}

				double[] start = Arrays.copyOf(tempRegion.getStart().getXn(), tempRegion.getStart().getXn().length);
				start[maxIndex] = midValue1;
				region2.setStart(new NPoint(start));
				region2.setEnd(tempRegion.getEnd());
				if (hasPointInRegion(region2)) {
					temp.add(region2);
				} else {
					untestedRegions.add(region2);
				}
			}
			testedRegions = temp;
		}
		// return count;
	}
}
