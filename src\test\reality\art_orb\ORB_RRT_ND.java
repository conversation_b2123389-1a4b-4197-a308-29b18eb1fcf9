package test.reality.art_orb;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

import Fault_10d.CalGCD;
import Fault_11d.Select;
import Fault_12d.Tcas;
//import Fault_12d.*;
import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import test.simulations.art_orb.ComplexRegion;
import tested.*;
import Fault_5d.*;
import Fault_6d.Triangle;
import Fault_8d.Line;

public class ORB_RRT_ND extends ART {
	
	double R;

	ArrayList<ComplexRegion> regions = new ArrayList<>();

	public ORB_RRT_ND(double[] min, double[] max, double R, FailurePattern failurePattern, Random random) {
		super(min, max, random, failurePattern);
		this.R = R;
	}

	

	public int findMaxRegion() {
		double maxSize = -1;
		int index = 0;
		for (int i = 0; i < regions.size(); i++) {
			if (regions.get(i).region.size() > maxSize) {
				maxSize = regions.get(i).region.size();
				index = i;
			}
		}
		return index;
	}

	public boolean isPointInRegion(NRectRegion region, NPoint p) {
		boolean flag = true;
		for (int i = 0; i < p.getXn().length; i++) {
			if (p.getXn()[i] < region.getStart().getXn()[i] || p.getXn()[i] > region.getEnd().getXn()[i]) {
				flag = false;
			}
		}
		return flag;
	}

	public NPoint randomTC() {
		// generate from the input domain
		NPoint point = new NPoint();
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setDimension(this.dimension);
		point.setXn(xn);
		return point;
	}

	public NPoint randomTC(ComplexRegion region) {
		double radius = calculateRadius(region.region);
		NPoint result = randomTC(region.region);
		boolean flag = true;
		while (flag) {
			flag = false;
			result = randomTC(region.region);

			// 排除区域是圆
			// 计算距离
			double[] tested = region.pointInRegion.getXn();
			double distance = 0;
			double[] untested = result.getXn();
			for (int j = 0; j < this.dimension; j++) {
				distance += Math.pow((tested[j] - untested[j]), 2);
			}
			distance = Math.sqrt(distance);
			if (distance < radius) {
				flag = true;
				// break;
			}
			/*
			 * //排除区域是正方形 if(Math.abs(p.p-tests.get(i).p)<radius){
			 * if(Math.abs(p.q-tests.get(i).q)<radius){ flag=true; } }
			 */
		}
		return result;
	}

	public NPoint randomTC(NRectRegion region) {
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];
		}
		NPoint result = new NPoint(xn);
		result.setDimension(this.dimension);
		return result;
	}

	public int run() {
		int count = 0;

		// first
		NPoint p = randomTC();
		ComplexRegion region = new ComplexRegion();
		region.region = new NRectRegion(new NPoint(min), new NPoint(max));
		region.pointInRegion = p;
		regions.add(region);
		if (!this.failPattern.isCorrect(p)) {
			return 1;
		}
		count++;
		//System.out.println(p);    ///
		// second
		NPoint t2 = randomTC();
		//System.out.println(t2);   ///
		splitRegion(p, t2, regions.get(0));
		regions.remove(0);

		while (this.failPattern.isCorrect(t2)) {
			count++;

			// 再生成一个测试用例t2
			int index = findMaxRegion();
			ComplexRegion maxRegion = regions.get(index);
			t2 = randomTC(maxRegion);
			//System.out.println(t2);   ///
			if (!this.failPattern.isCorrect(t2)) {
				break;
			}
			// split region
			regions.remove(index);
			splitRegion(maxRegion.pointInRegion, t2, maxRegion);
		}
		return count;
	}

	public void splitRegion(NPoint p, NPoint t2, ComplexRegion region) {
		double[] pn = p.getXn();
		double[] t2n = t2.getXn();

		double[] mid = new double[pn.length];
		for (int i = 0; i < mid.length; i++) {
			mid[i] = (pn[i] + t2n[i]) / 2.0;
		}
		// System.out.println("middle point:"+Arrays.toString(mid));
		// 找到最大的边
		double maxBian = 0.0;
		int maxIndex = 0;
		for (int i = 0; i < region.region.getStart().getXn().length; i++) {
			if (region.region.getEnd().getXn()[i] - region.region.getStart().getXn()[i] > maxBian) {
				maxBian = region.region.getEnd().getXn()[i] - region.region.getStart().getXn()[i];
				maxIndex = i;
			}
		}

		// 一分为二
		NRectRegion region1 = new NRectRegion();
		NRectRegion region2 = new NRectRegion();

		region1.setStart(region.region.getStart());
		double[] end = Arrays.copyOf(region.region.getEnd().getXn(), region.region.getEnd().getXn().length);
		end[maxIndex] = mid[maxIndex];
		region1.setEnd(new NPoint(end));

		double[] start = Arrays.copyOf(region.region.getStart().getXn(), region.region.getStart().getXn().length);
		start[maxIndex] = mid[maxIndex];
		region2.setStart(new NPoint(start));
		region2.setEnd(region.region.getEnd());
		// if (maxIndex == 0) {// x轴长
		// region1.setStart(region.region.getStart());
		// region1.setEnd(new NPoint(new double[] { mid[0],
		// region.region.getEnd().getXn()[1] }));
		//
		// region2.setStart(new NPoint(new double[] { mid[0],
		// region.region.getStart().getXn()[1] }));
		// region2.setEnd(region.region.getEnd());
		// } else if (maxIndex == 1) {// y轴长
		// region1.setStart(region.region.getStart());
		// region1.setEnd(new NPoint(new double[] { region.region.getEnd().getXn()[0],
		// mid[1] }));
		//
		// region2.setStart(new NPoint(new double[] {
		// region.region.getStart().getXn()[0], mid[1] }));
		// region2.setEnd(region.region.getEnd());
		// }
		ComplexRegion cr1 = new ComplexRegion();
		cr1.region = region1;
		if (isPointInRegion(region1, p)) {
			cr1.pointInRegion = p;
		} else {
			cr1.pointInRegion = t2;
		}
		ComplexRegion cr2 = new ComplexRegion();
		cr2.region = region2;
		if (isPointInRegion(region2, t2)) {
			cr2.pointInRegion = t2;
		} else {
			cr2.pointInRegion = p;
		}
		regions.add(cr1);
		regions.add(cr2);
	}
	
	
	public double calculateRadius(NRectRegion region) {
		// 二维的
		//return Math.sqrt(R * region.size() / (Math.PI));
				//One D
				return (R * region.size()) / 2;
				//Two D
				//return Math.pow((R * region.size() / (Math.PI)),0.5);       
				//Three D
				//return Math.pow(((0.75 * (region.size() * R)) /  (Math.PI)), 1.0 / 3.0);
				//Four D
				//return Math.pow(((2*R * region.size()) / (Math.PI*Math.PI)),1.0/4.0);
				//5 D
				//return Math.pow(((1.875*R * region.size()) / (Math.pow(Math.PI, 2))),1.0/5.0);
				//6 D
				//return Math.pow(((6*R * region.size()) / (Math.pow(Math.PI, 3))),1.0/6.0);
				//8 D
				//return Math.pow(((24*R * region.size()) / (Math.pow(Math.PI, 4))),1.0/8.0);
				//10 D
				//return Math.pow(((120*R * region.size()) / (Math.pow(Math.PI, 5))),1.0/10.0);
				//11 D
				//return Math.pow(((162.422*R * region.size()) / (Math.pow(Math.PI, 5))),1.0/11.0);
				//12 D
				//return Math.pow(((720*R * region.size()) / (Math.pow(Math.PI, 6))),1.0/12.0);
	}
	
	public static void main(String[] args) {
		airy beTested=new airy();
	    //tanh beTested=new tanh();
	   // erfcc beTested=new erfcc();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		//Tcas beTested = new Tcas();
		//Select beTested = new Select();
		//Triangle beTested = new Triangle();
		//Line beTested = new Line();
		//CalDay beTested = new CalDay();
		//CalGCD beTested = new CalGCD();
		
			
		double[] min = beTested.min;
		double[] max = beTested.max;
		int times = 10;
		int fm = 0;
		FailurePattern pattern = new RealityFailPattern(beTested.getClass().getSimpleName());
		pattern.min = min;
		pattern.max = max;
		pattern.fail_rate = beTested.failureRate;
		pattern.dimension = beTested.Dimension;
		
		long startTime1 = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
//			long startTime = System.currentTimeMillis();

			ORB_RRT_ND test = new ORB_RRT_ND(min, max, 0.8, pattern, new Random(i * 5 + 3));
			int temp = test.run();
			fm += temp;
//			long endTime = System.currentTimeMillis();
//			System.out.println(endTime - startTime); 
		}
		long endTime1 = System.currentTimeMillis();
		System.out.println("Fmeasure: "+ fm / (double) times);
		System.out.println("Time: "+(endTime1 - startTime1) / (double) times);
	}
}
