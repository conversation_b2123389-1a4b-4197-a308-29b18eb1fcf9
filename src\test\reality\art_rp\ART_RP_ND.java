package test.reality.art_rp;

import java.util.ArrayList;
import java.util.Random;

import Fault_10d.NearestDistance;
import Fault_11d.Select;
import Fault_5d.CalDay;
import Fault_6d.Complex;
import Fault_6d.Triangle;
import Fault_8d.Line;
import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.TD.Point;
import datastructure.TD.Region;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import tested.*;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_RP_ND extends ART{
	public NRectRegion initRegion;
	ArrayList<NRectRegion> regions = new ArrayList<>();
	public ART_RP_ND( double[] min,double[] max,Random random,FailurePattern pattern,NRectRegion region) {
		super(min,max,random,pattern);
		initRegion=region;
	}
	public int run() {
		int count = 0;
		regions.add(initRegion);
		NPoint p=genNextP(random,initRegion);
		//while(isCorrect(p)){
			while(this.failPattern.isCorrect(p)){
			count++;
			// 找出最大区域
			double maxsize = 0;
			NRectRegion maxregion = null;
			int maxregion_index = 0;
			for (int i = 0; i < regions.size(); i++) {
				NRectRegion temp = regions.get(i);
				if (temp.size() > maxsize) {
					maxsize = temp.size();
					maxregion = temp;
					maxregion_index = i;
				}
			}
			//
			regions.remove(maxregion_index);
			//// generate next one test case
			p = new NPoint();
			p=genNextP(random, maxregion);
			//add 2^m 次方的
			int quyus=(int)Math.pow(2, dimension);
			ArrayList<double[]> lists=PaiLie.GetAll(maxregion.getStart().getXn(), maxregion.getEnd().getXn());
			for (int i = 0; i < quyus; i++) {
				NRectRegion region = new NRectRegion(p,new NPoint(lists.get(i)));
				regions.add(region);
			}
			
			//2维特殊化
			//addRegionsIn2D(maxregion, p);
		}
		return count;
	}
//	public void addRegionsIn2D(NRectRegion region,NPoint p){
//		double xmin=region.getStart().getXn()[0];
//		double ymin=region.getStart().getXn()[1];
//		double xmax=region.getEnd().getXn()[0];
//		double ymax=region.getEnd().getXn()[1];
//		double pp=p.getXn()[0];
//		double qq=p.getXn()[1];
//		
//		NRectRegion first = new NRectRegion(new NPoint(new double[] { xmin, ymin }),
//				new NPoint(new double[] { pp, qq }));
//		NRectRegion second = new NRectRegion(new NPoint(new double[] { pp, ymin }),
//				new NPoint(new double[] { xmax, qq }));
//		NRectRegion third = new NRectRegion(new NPoint(new double[] { pp, qq }),
//				new NPoint(new double[] { xmax, ymax }));
//		NRectRegion fourth = new NRectRegion(new NPoint(new double[] { xmin, qq }),
//				new NPoint(new double[] { pp, ymax }));
//	
//		this.regions.add(first);
//		this.regions.add(second);
//		this.regions.add(third);
//		this.regions.add(fourth);
//	}
	public NPoint genNextP(Random random,NRectRegion region){
		NPoint p=new NPoint();
		double[] start=region.getStart().getXn();
		double[] end=region.getEnd().getXn();
		double xn[]=new double[dimension];
		
		for (int i = 0; i < xn.length; i++) {
			xn[i]=random.nextDouble()*(end[i]-start[i])+start[i];
		}
		p.setXn(xn);
		return p;
	}
	public static void main(String[] args) {
		//一定要修改这个
		//airy beTested=new airy();
		//tanh beTested=new tanh();
		//erfcc beTested=new erfcc();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		//Tcas beTested = new Tcas();
		//Select beTested = new Select();
		//Triangle beTested = new Triangle();
		Line beTested = new Line();
		//CalDay beTested = new CalDay();
		//CalGCD beTested = new CalGCD();
		//Complex beTested = new Complex();
		//NearestDistance beTested = new NearestDistance();
		
		int d = beTested.Dimension;
		double start[] = beTested.min;
		double end[] = beTested.max;
		NRectRegion initRegion=new NRectRegion();
		initRegion.setStart(new NPoint(start));
		initRegion.setEnd(new NPoint(end));
		
		int times=3000;
		long sums = 0;
		FailurePattern failurePattern=new RealityFailPattern(beTested.getClass().getSimpleName());
		failurePattern.fail_rate =beTested.failureRate;
		failurePattern.min = start;
		failurePattern.max = end;
		failurePattern.dimension = d;
		
		
		
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			
			ART_RP_ND art_rp_nd = new ART_RP_ND(start,end,new Random(i*3),failurePattern,initRegion);
			int fm =art_rp_nd.run();
			sums += fm;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: "+ sums / (double) times);
		System.out.println("Time: "+(endTime - startTime) / (double) times);
	}
}
