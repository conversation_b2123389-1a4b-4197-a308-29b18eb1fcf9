package test.reality.art_rp;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import Fault_11d.Select;
import Fault_5d.CalDay;
import Fault_6d.Triangle;
import Fault_8d.Line;
import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.TD.Point;
import datastructure.TD.Region;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import tested.*;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_RP_ND_Test extends ART{
	public NRectRegion initRegion;
	ArrayList<NRectRegion> regions = new ArrayList<>();
	public ART_RP_ND_Test( double[] min,double[] max,Random random,FailurePattern pattern,NRectRegion region) {
		super(min,max,random,pattern);
		initRegion=region;
	}
	public int run() {
		int count = 0;
		regions.add(initRegion);
		NPoint p=genNextP(random,initRegion);
		//while(isCorrect(p)){
			while(this.failPattern.isCorrect(p)){
			count++;
			// 找出最大区域
			double maxsize = 0;
			NRectRegion maxregion = null;
			int maxregion_index = 0;
			for (int i = 0; i < regions.size(); i++) {
				NRectRegion temp = regions.get(i);
				if (temp.size() > maxsize) {
					maxsize = temp.size();
					maxregion = temp;
					maxregion_index = i;
				}
			}
			//
			regions.remove(maxregion_index);
			//// generate next one test case
			p = new NPoint();
			p=genNextP(random, maxregion);
			//add 2^m 次方的
//			int quyus=(int)Math.pow(2, dimension);
//			ArrayList<double[]> lists=PaiLie.GetAll(maxregion.getStart().getXn(), maxregion.getEnd().getXn());
//			for (int i = 0; i < quyus; i++) {
//				NRectRegion region = new NRectRegion(p,new NPoint(lists.get(i)));
//				regions.add(region);
//			}
			
			try {
				addRegionsInND(maxregion, p);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return count;
	}
	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
		// int regions=(int) Math.pow(2, this.dimension);
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double[] pxn = p.getXn();
		List<List<Double>> result1 = splitRegions(start, pxn);
		List<List<Double>> result2 = splitRegions(pxn, end);
		// System.out.println(result1.size());
		if (result1.size() != result2.size()) {
			throw new Exception("result1's size!=result2's size ,split region wrong");
		}
		for (int i = 0; i < result1.size(); i++) {
			List<Double> temp1 = result1.get(i);
			List<Double> temp2 = result2.get(i);
			double[] newStart = new double[temp1.size()];
			double[] newEnd = new double[temp2.size()];
			for (int j = 0; j < temp1.size(); j++) {
				newStart[j] = temp1.get(j);
				newEnd[j] = temp2.get(j);
			}

			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));
			this.regions.add(tempRegion);
		}
	}

	public List<List<Double>> splitRegions(double[] start, double[] end) {
		ArrayList<double[]> values = new ArrayList<>();
		for (int i = 0; i < start.length; i++) {
			double[] temp = new double[2];

			temp[0] = start[i];
			temp[1] = end[i];
			values.add(temp);
		}

		ArrayList<List<Double>> result = new ArrayList<>();
		PaiLie.per(values, 0, new ArrayList<>(), result);
		return result;
	}
	
	public NPoint genNextP(Random random,NRectRegion region){
		NPoint p=new NPoint();
		double[] start=region.getStart().getXn();
		double[] end=region.getEnd().getXn();
		double xn[]=new double[dimension];
		
		for (int i = 0; i < xn.length; i++) {
			xn[i]=random.nextDouble()*(end[i]-start[i])+start[i];
		}
		p.setXn(xn);
		return p;
	}
	public static void main(String[] args) {
		//一定要修改这个
		//airy beTested=new airy();
		tanh beTested=new tanh();
		//erfcc beTested=new erfcc();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		//Tcas beTested = new Tcas();
		//Select beTested = new Select();
		//Triangle beTested = new Triangle();
		//Line beTested = new Line();
		//CalDay beTested = new CalDay();
		//CalGCD beTested = new CalGCD();
		
		int d = beTested.Dimension;
		double start[] = beTested.min;
		double end[] = beTested.max;
		NRectRegion initRegion=new NRectRegion();
		initRegion.setStart(new NPoint(start));
		initRegion.setEnd(new NPoint(end));
		
		int times=3000;
		long sums = 0;
		FailurePattern failurePattern=new RealityFailPattern(beTested.getClass().getSimpleName());
		failurePattern.fail_rate =beTested.failureRate;
		failurePattern.min = start;
		failurePattern.max = end;
		failurePattern.dimension = d;
		
		
		
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			
			ART_RP_ND_Test art_rp_nd = new ART_RP_ND_Test(start,end,new Random(i*3),failurePattern,initRegion);
			int fm =art_rp_nd.run();
			sums += fm;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: "+sums / (double) times);
		System.out.println("Time: "+(endTime - startTime) / (double) times);
	}
}
