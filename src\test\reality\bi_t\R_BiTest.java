package test.reality.bi_t;

import tested.erfcc;

public class R_BiTest {
	public static void main(String[] args) {
		int cishu = 2000;
		long sumOfF = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < cishu; i++) {
			R_BiTest bi_t = new R_BiTest(-30000, 30000);
			int f_measure = bi_t.run();
			sumOfF += f_measure;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: " + sumOfF / (double) cishu);
		System.out.println("Time: " + (endTime - startTime) / (double) cishu);

		// 实验效果 证明并不可行

	}
	double start;

	double end;

	public R_BiTest(double start, double end) {
		this.start = start;
		this.end = end;
	}

	public int run() {

		int count = 0;
		erfcc betest = new erfcc();
		double p = 0.5;
		double reallyP = (end - start) * 0.5;
		int i = 1, m = 0;
		while (betest.isCorrect(reallyP)) {
			count++;
			m = (int) (count + 1 - Math.pow(2, i));
			p = (Math.pow(2, -(i + 1))) * (2 * m + 1);
			reallyP = p * (end - start);
			if (2 * m + 1 == (Math.pow(2, i + 1)) - 1) {
				i++;
			}

		}
		return count;
	}
}
