package test.reality.bi_t;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import tested.tan0;

public class R_BiTest2 {
	public static void main(String[] args) {
		int cishu = 10000;
		long sumOfF = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < cishu; i++) {
			R_BiTest2 bi_t = new R_BiTest2(-500, 500, i * 6);
			int f_measure = bi_t.run();
			sumOfF += f_measure;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: " + sumOfF / (double) cishu);
		System.out.println("Time: " + (endTime - startTime) / (double) cishu);

		// 实验效果 证明并不可行

	}
	double start;
	double end;

	long seed;

	public R_BiTest2(double start, double end, long seed) {
		this.start = start;
		this.end = end;
		this.seed = seed;
	}

	public int run() {
		Random random = new Random(seed);
		int count = 0;
		int count2 = 0;
		tan0 betest = new tan0();
		double p = 0.5;
		double reallyP = (end + start) * 0.5;
		int i = 1, m = 0;
		boolean flag = true;
		ArrayList<TestCase> tests = new ArrayList<>();
		if (!betest.isCorrect(reallyP)) {
			return 1;
		}
		while (flag) {
			count++;
			m = (int) (count + 1 - Math.pow(2, i));
			p = (Math.pow(2, -(i + 1))) * (2 * m + 1);
			reallyP = p * (end - start) + start;
			reallyP = reallyP
					+ (random.nextDouble() * (Math.pow(2, 1 - i) - Math.pow(2, -1 - i)) + Math.pow(2, -i - 1));
			tests.add(new TestCase(reallyP));
			if (2 * m + 1 == (Math.pow(2, i + 1)) - 1) {
				i++;
				ArrayList<TestCase> temp = new ArrayList<>(tests);
				for (int l = 0; l < tests.size(); l++) {

					int index = random.nextInt(temp.size());
					// System.out.println(temp.get(index).p);
					count2++;
					if (!betest.isCorrect(temp.get(index).p)) {
						flag = false;
						return count2;
					}
					temp.remove(index);
				}
			}

		}
		return count;
	}
}
