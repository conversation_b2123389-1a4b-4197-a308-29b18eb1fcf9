package test.reality.ddr._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import tested.airy;

/**
 * run configutation: VM arguments:
 * -Djava.library.path="${workspace_loc}/ART/Resource;${env_var:PATH}"
 */

public class DDR_OD_JNI {
	public static void main(String[] args) throws Exception {
		int times = 3000;
		long sums = 0;
		int temp = 0;
		int s = 10;
		airy tested = new airy();
		// probks0 tested = new probks0();
		//////////////
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			DDR_OD_JNI rrt_od = new DDR_OD_JNI(tested.min[0], tested.max[1], 0.75 * (tested.max[0] - tested.min[0]), s,
					tested.getClass().getName(), i * 3);// 0.002 refers to failure rate
			temp = rrt_od.run();
			sums += temp;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: " + sums / (double) times);
		System.out.println("Time: " + (endTime - startTime) / (double) times);
	}
	double min;
	double max;
	// double fail_start;
	String ClassName;
	// double fail_rate;
	double R;
	int s;
	int randomseed;

	ArrayList<TestCase> tests = new ArrayList<>();

	public DDR_OD_JNI(double min, double max, double r, int s, String ClassName, int randomseed) {
		super();
		this.min = min;
		this.max = max;
		R = r;
		this.s = s;
		this.ClassName = ClassName;
		this.randomseed = randomseed;
	}

	public TestCase randomTC(Random random) {
		TestCase temp = new TestCase();
		double temp_value = random.nextDouble() * (max - min) + min;
		temp.p = temp_value;
		return temp;
	}

	public int run() throws Exception {
		Random random = new Random(randomseed);
		Class<?> classes = Class.forName(this.ClassName);
		int count = 0;// 记录测试用例数量
		int _10CandidateCount = 1;// 每十个一次的数量
		TestCase p = randomTC(random);// 第一个测试用例
		/*
		 * System.out.println("p0:" + p.p);
		 * System.out.println(Boolean.parseBoolean(classes.getMethod("isCorrect",
		 * double.class).invoke(classes.newInstance(), p.p).toString()));
		 */
		while (Boolean.parseBoolean(
				classes.getMethod("isCorrect", double.class).invoke(classes.newInstance(), p.p).toString())) {
			double radius = R / (2 * (s + _10CandidateCount));
			// 生成s个候选测试用例,从s个候选测试用例中挑选符合要求的测试用例
			p = new TestCase();
			boolean all_s_has_E_flag = true;
			double TS2C[] = new double[s];
			double Cvalue[] = new double[s];
			for (int k = 0; k < s; k++) {
				// 生成一个候选测试用例
				TestCase ck = randomTC(random);
				boolean this_ck_has_E_flag = false;
				double min = Double.MAX_VALUE;
				for (int i = 0; i < tests.size(); i++) {

					if ((ck.p > (tests.get(i).p - radius) && (ck.p < (tests.get(i).p + radius)))) {
						if (min > Math.abs(ck.p - tests.get(i).p)) {
							min = Math.abs(ck.p - tests.get(i).p);
							TS2C[k] = min;
							Cvalue[k] = ck.p;
						}
						this_ck_has_E_flag = true;
					}
				}
				if (!this_ck_has_E_flag) {
					all_s_has_E_flag = false;
					p = new TestCase();
					p.p = ck.p;
					/*
					 * System.out.println("p" + count + ":" + p.p + "  rrt");
					 * System.out.println(Boolean.parseBoolean(classes.getMethod("isCorrect",
					 * double.class).invoke(classes.newInstance(), p.p).toString()));
					 */
					if (!Boolean.parseBoolean(classes.getMethod("isCorrect", double.class)
							.invoke(classes.newInstance(), p.p).toString())) {
						// System.out.println("err" + p.p);
						return count;
					} else {
						count++;
						tests.add(p);
						/*
						 * if(k!=s-1) p = new TestCase();
						 */
					}
				}

			}
			if (all_s_has_E_flag) {
				double max = 0;
				int index = 0;
				for (int i = 0; i < TS2C.length; i++) {
					if (max < TS2C[i]) {
						max = TS2C[i];
						index = i;
					}
				}
				p = new TestCase();
				p.p = Cvalue[index];
				if (!Boolean.parseBoolean(
						classes.getMethod("isCorrect", double.class).invoke(classes.newInstance(), p.p).toString())) {
					// System.out.println("err" + p.p);
					return count;
				} else {
					count++;
					tests.add(p);
					// p = new TestCase();
				}
			}
			_10CandidateCount++;
			// System.out.println("candidate++");
		}
		return count;
	}
}
