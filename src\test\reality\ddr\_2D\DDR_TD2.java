package test.reality.ddr._2D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import tested.bessj;

public class DDR_TD2 {
	public static void main(String[] args) throws Exception {
		int times = 100;
		long sums = 0;
		int temp = 0;
		int s = 10;
		bessj tested = new bessj();
		// probks0 tested = new probks0();
		//////////////
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			DDR_TD2 rrt_od = new DDR_TD2(tested.min, tested.max, 0.75, s, tested.getClass().getName(), i * 3);// 0.002
																												// refers
																												// to
																												// failure
																												// rate
			temp = rrt_od.run();
			sums += temp;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: " + sums / (double) times);
		System.out.println("Time: " + (endTime - startTime) / (double) times);
	}
	double[] min;
	double[] max;
	// double fail_start;
	String ClassName;
	// double fail_rate;
	double R;
	int s;
	int randomseed;

	ArrayList<TestCase> tests = new ArrayList<>();

	public DDR_TD2(double[] min, double[] max, double r, int s, String ClassName, int randomseed) {
		super();
		this.min = min;
		this.max = max;
		R = r;
		this.s = s;
		this.ClassName = ClassName;
		this.randomseed = randomseed;
	}

	public TestCase randomTC(Random random) {
		TestCase temp = new TestCase();
		double p = random.nextDouble() * (max[0] - min[0]) + min[0];
		double q = random.nextDouble() * (max[1] - min[1]) + min[1];
		temp.p = p;
		temp.q = q;
		return temp;
	}

	public int run() throws Exception {
		Random random = new Random(randomseed);
		Class<?> classes = Class.forName(this.ClassName);
		int count = 0;// 记录测试用例数量
		int _10CandidateCount = 1;// 每十个一次的数量
		TestCase p = randomTC(random);// 第一个测试用例
		/*
		 * System.out.println("p0:" + p.p);
		 * System.out.println(Boolean.parseBoolean(classes.getMethod("isCorrect",
		 * double.class).invoke(classes.newInstance(), p.p).toString()));
		 */
		while (Boolean.parseBoolean(classes.getMethod("isCorrect", double.class, double.class)
				.invoke(classes.newInstance(), p.p, p.q).toString())) {
			double radius = Math.sqrt(R * (max[1] - min[1]) * (max[0] - min[0]) / (Math.PI * (s + _10CandidateCount)));
			// 生成s个候选测试用例,从s个候选测试用例中挑选符合要求的测试用例
			p = new TestCase();
			boolean all_s_has_E_flag = true;
			double TS2C[] = new double[s];
			double Cvalue[] = new double[s];
			double Qvalue[] = new double[s];
			for (int k = 0; k < s; k++) {
				// 生成一个候选测试用例
				TestCase ck = randomTC(random);
				boolean this_ck_has_E_flag = false;
				double min = Double.MAX_VALUE;
				for (int i = 0; i < tests.size(); i++) {

					if ((Math.pow((ck.p - tests.get(i).p) * (ck.p - tests.get(i).p)
							+ (ck.q - tests.get(i).q) * (ck.q - tests.get(i).q), 0.5)) < radius) {
						if (min > Math.sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q, 2))) {
							min = Math.sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q, 2));
							TS2C[k] = min;
							Cvalue[k] = ck.p;
							Qvalue[k] = ck.q;
						}
						this_ck_has_E_flag = true;
					}
				}
				if (!this_ck_has_E_flag) {
					all_s_has_E_flag = false;
					p = new TestCase();
					p.p = ck.p;
					p.q = ck.q;
					/*
					 * System.out.println("p" + count + ":" + p.p + "  rrt");
					 * System.out.println(Boolean.parseBoolean(classes.getMethod("isCorrect",
					 * double.class).invoke(classes.newInstance(), p.p).toString()));
					 */
					if (!Boolean.parseBoolean(classes.getMethod("isCorrect", double.class, double.class)
							.invoke(classes.newInstance(), p.p, p.q).toString())) {
						// System.out.println("err" + p.p);
						return count;
					} else {
						count++;
						tests.add(p);
					}
				}

			}
			if (all_s_has_E_flag) {
				double max = 0;
				int index = 0;
				for (int i = 0; i < TS2C.length; i++) {
					if (max < TS2C[i]) {
						max = TS2C[i];
						index = i;
					}
				}
				p = new TestCase();
				p.p = Cvalue[index];
				p.q = Qvalue[index];
				if (!Boolean.parseBoolean(classes.getMethod("isCorrect", double.class, double.class)
						.invoke(classes.newInstance(), p.p, p.q).toString())) {
					return count;
				} else {
					count++;
					tests.add(p);
				}
			}
			_10CandidateCount++;
		}
		return count;
	}
}
