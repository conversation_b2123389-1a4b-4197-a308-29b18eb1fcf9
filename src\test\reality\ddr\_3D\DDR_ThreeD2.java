package test.reality.ddr._3D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import tested.golden;

public class DDR_ThreeD2 {
	public static void main(String[] args) throws Exception {
		int times = 100;
		long sums = 0;
		int temp = 0;
		int s = 10;
		golden tested = new golden();
		//////////////
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			DDR_ThreeD2 rrt_od = new DDR_ThreeD2(tested.min, tested.max, 0.75, s, tested.getClass().getName(), i * 3);
			temp = rrt_od.run();
			sums += temp;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: " + sums / (double) times);
		System.out.println("Time: " + (endTime - startTime) / (double) times);
	}
	double[] min;
	double[] max;
	double R;
	int s;
	String className;
	int randomseed;

	ArrayList<TestCase> tests = new ArrayList<>();

	public DDR_ThreeD2(double[] min, double[] max, double r, int s, String ClassName, int randomseed) {
		super();
		this.min = min;
		this.max = max;
		R = r;
		this.className = ClassName;
		this.s = s;
		this.randomseed = randomseed;
	}

	public TestCase randomTC(Random random) {
		TestCase temp = new TestCase();
		double p = random.nextDouble() * (max[0] - min[0]) + min[0];
		double q = random.nextDouble() * (max[1] - min[1]) + min[1];
		double m = random.nextDouble() * (max[2] - min[2]) + min[2];
		temp.p = p;
		temp.q = q;
		temp.m = m;
		return temp;
	}

	public int run() throws Exception {
		Random random = new Random(randomseed);
		int count = 0;
		int _10CandidateCount = 0;
		TestCase p = randomTC(random);// randomTC
		Class<?> classes = Class.forName(this.className);
		while (Boolean.parseBoolean(classes.getMethod("isCorrect", double.class, double.class, double.class)
				.invoke(classes.newInstance(), p.p, p.q, p.m).toString())) {
			double InputDomainA = 1.0;
			for (int i = 0; i < this.min.length; i++) {
				InputDomainA *= max[i] - min[i];
			}
			double radius = Math.pow((0.75 * (InputDomainA * R)) / ((_10CandidateCount + s) * (Math.PI)), 1.0 / 3.0);
			p = new TestCase();
			boolean all_s_has_E_flag = true;
			double TS2C[] = new double[s];
			double Pvalue[] = new double[s];
			double Qvalue[] = new double[s];
			double Mvalue[] = new double[s];

			for (int k = 0; k < s; k++) {
				TestCase ck = randomTC(random);
				boolean this_ck_has_E_flag = false;
				double min = Double.MAX_VALUE;
				for (int i = 0; i < tests.size(); i++) {
					// 没有在圈之中
					double distance = Math.pow((Math.pow((ck.p - tests.get(i).p), 2.0))
							+ (Math.pow(ck.q - tests.get(i).q, 2.0)) + (Math.pow(ck.m - tests.get(i).m, 2.0)), 0.5);// distance
					if (distance < radius) {
						if (min > distance) {
							min = distance;
							TS2C[k] = min;
							Pvalue[k] = ck.p;
							Qvalue[k] = ck.q;
							Mvalue[k] = ck.m;
						}
						this_ck_has_E_flag = true;
					}
				}
				if (!this_ck_has_E_flag) {
					all_s_has_E_flag = false;
					p = new TestCase();
					p.p = ck.p;
					p.q = ck.q;
					p.m = ck.m;
					if (!Boolean.parseBoolean(classes.getMethod("isCorrect", double.class, double.class, double.class)
							.invoke(classes.newInstance(), p.p, p.q, p.m).toString())) {
						return count;
					} else {
						count++;
						tests.add(p);
					}
				}
			}
			if (all_s_has_E_flag) {
				double max = 0;
				int index = 0;
				for (int i = 0; i < TS2C.length; i++) {
					if (max < TS2C[i]) {
						max = TS2C[i];
						index = i;
					}
				}
				p = new TestCase();
				p.p = Pvalue[index];
				p.q = Qvalue[index];
				p.m = Mvalue[index];
				if (!Boolean.parseBoolean(classes.getMethod("isCorrect", double.class, double.class, double.class)
						.invoke(classes.newInstance(), p.p, p.q, p.m).toString())) {
					return count;
				} else {
					count++;
					tests.add(p);
				}
			}
			_10CandidateCount++;
		}
		return count;
	}
}
