package test.reality.fscs;
/*
 * n维实现,包含1维2维等
 * */

import java.util.ArrayList;
import java.util.Random;

import Fault_8d.Line;
import Fault_8d.TwoLinesPos;
import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import Fault_10d.CalGCD;
import Fault_10d.NearestDistance;
import Fault_11d.Select;
import Fault_12d.Tcas;
//import Fault_12d.*;
import test.ART;
import tested.*;
import Fault_5d.*;
import Fault_6d.Complex;
import Fault_6d.Triangle;
import Fault_8d.Line;

public class FSCS_ND extends ART {
	public static void main(String[] args) {
		int times = 3000;
		// int d = 3;
		// double fail_rate = 0.005;
		// ZeroOneCreator dataCreator = new ZeroOneCreator();
		//airy beTested=new airy();
	    //tanh beTested=new tanh();
	    //erfcc beTested=new erfcc();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		//Tcas beTested = new Tcas();
		//Select beTested = new Select();
		//Triangle beTested = new Triangle();
		//Complex beTested = new Complex();
		//Line beTested = new Line();
		CalDay beTested = new CalDay();
		//CalGCD beTested = new CalGCD();
		//TwoLinesPos beTested = new TwoLinesPos();
		//NearestDistance beTested = new NearestDistance();
		
		double[] min = beTested.min;
		double[] max = beTested.max;
		int s = 10;
		FailurePattern failurePattern = new RealityFailPattern(beTested.getClass().getSimpleName());
		// FailurePattern failurePattern = new StripPatternIn2D();
		failurePattern.fail_rate = beTested.failureRate;
		failurePattern.min = min;
		failurePattern.max = max;
		failurePattern.dimension = beTested.Dimension;

		int fm = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			FSCS_ND test = new FSCS_ND(min, max, s, failurePattern, new Random(i * 3));
			int temp = test.run();
			fm += temp;
		}
		long endTime = System.currentTimeMillis();
		System.out.println(fm / (double) times);
		System.out.println((endTime - startTime) / (double) times);
	}
	int s = 10;// 表示候选集的数量初始值为10

	ArrayList<NPoint> tests = new ArrayList<>();

	public FSCS_ND(double[] min, double[] max, int s, FailurePattern pattern, Random random) {
		super(min, max, random, pattern);
		this.s = s;
	}

	public double calTwoPointDistance(NPoint p1, NPoint p2) {
		double[] p1xn = p1.getXn();
		double[] p2xn = p2.getXn();
		double distance = 0.0;
		for (int i = 0; i < p1xn.length; i++) {
			distance += Math.pow((p2xn[i] - p1xn[i]), 2);
		}
		distance = Math.sqrt(distance);
		return distance;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		NPoint p = randomTC();
		// while (this.failPattern.isCorrect(p)) {
		while (this.failPattern.isCorrect(p)) {
			count++;
			tests.add(p);
			p = new NPoint();
			double maxDistance = -1.0;
			NPoint bestCandidate = null;
			for (int i = 0; i < s; i++) {
				NPoint candidate = randomTC();
				// 计算两个点的距离
				double minDistance = Double.MAX_VALUE;

				for (int j = 0; j < this.tests.size(); j++) {
					double tempDistance = calTwoPointDistance(candidate, tests.get(j));
					if (tempDistance < minDistance) {
						minDistance = tempDistance;
					}
				}
				if (maxDistance < minDistance) {
					maxDistance = minDistance;
					bestCandidate = candidate;
				}
			}
			p = null;
			p = bestCandidate;
		}
		return count;
	}
}
