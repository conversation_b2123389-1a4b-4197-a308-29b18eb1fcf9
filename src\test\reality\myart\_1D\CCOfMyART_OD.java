package test.reality.myart._1D;

import java.util.ArrayList;
import java.util.Random;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import coverage.factory.CoverageFactory;
import datastructure.TD.TestCase;
import tested.bessj0;
import tested.erfcc;

public class CCOfMyART_OD {

	public static int ZUOQUXIAN = 1;
	public static int YOUQUXIAN = 2;
	private static Logger logger = LogManager.getLogger("CCofMyART2_OD");
	public static void main(String[] args) throws Exception {
		int times = 3000;

		long sums = 0;
		int temp = 0;
		long startTime = System.currentTimeMillis();
		/*
		 * String correctTest=new MySimulation0().getClass().getName(); String
		 * wrongTest=new MySimulation1().getClass().getName();
		 */
		String correctTest = new erfcc().getClass().getName();
		String wrongTest = new erfcc().getClass().getName();
		double min = new bessj0().min[0];
		double max = new bessj0().max[0];
		for (int i = 0; i < times; i++) {
			CCOfMyART_OD ccrr = new CCOfMyART_OD(min, max, i * 9, correctTest, wrongTest);
			temp = ccrr.test();
			sums += temp;
			if (i % 500 == 0) {
				System.out.println("i:" + i);
			}
		}
		long endTime = System.currentTimeMillis();

		System.out.println("Fm: " + sums / (double) times);
		System.out.println("Time: " + (endTime - startTime) / (double) times);
	}
	double min;
	double max;
	int seed;
	String CorrectClassName;
	String WrongClassName;

	ArrayList<TestCase> tests = new ArrayList<>();

	CoverageFactory coverageFactory = new CoverageFactory();

	public CCOfMyART_OD(double min, double max, int seed, String ClassName, String WrongClassName) {
		// super();
		this.min = min;
		this.max = max;
		this.seed = seed;
		this.CorrectClassName = ClassName;
		this.WrongClassName = WrongClassName;
		coverageFactory.setClassName(WrongClassName);
	}

	public void sortTestCases(TestCase p) {
		int low = 0, high = tests.size() - 1, mid = -1;
		while (low <= high) {
			mid = (low + high) / 2;
			if (p.p > tests.get(mid).p) {
				low = mid + 1;
			} else {
				high = mid - 1;
			}
		}
		if (p.p < tests.get(mid).p) {
			mid = mid - 1;
		}
		tests.add(mid + 1, p);
	}

	/**
	 * @return F-measure
	 * @throws Exception
	 */
	public int test() throws Exception {
		Random random = new Random(seed);

		Class<?> classes = Class.forName(CorrectClassName);
		int count = 0;
		TestCase p = new TestCase();
		// 执行第一个测试用例
		p.p = random.nextDouble() * (max - min) + min;
		// 得到该测试用例的代码覆盖率
		// p.coverage = coverageFactory.execute(p.p);// coverageFactory.execute(p.p);
		p.coverage = 10;
		// System.out.println(p.toString()+" count:"+count);
		// logger.info(p.toString()+" count:"+count);
		// while(count<10){
		while (Boolean.parseBoolean(
				classes.getMethod("isCorrect", double.class).invoke(classes.newInstance(), p.p).toString())) {
			count++;
			if (tests.size() == 0) {
				tests.add(p);
			} else
				sortTestCases(p);
			///
			double datum_line;// 基准线，待会求出
			double tempProbability = 0.0;
			double sumProbability = 0.0;
			ArrayList<double[]> integrals = new ArrayList<>();
			/// 下面产生下一个测试用例,根据自己的概率曲线图
			// 先求第一段
			tempProbability = Math.pow((tests.get(0).p - min), 2.0) / (tests.get(0).coverage + 1.0);
			sumProbability += tempProbability;
			double[] informations = new double[5];
			informations[0] = tempProbability;
			informations[1] = ZUOQUXIAN;
			informations[2] = min;
			informations[3] = tests.get(0).p;
			informations[4] = tests.get(0).coverage;
			integrals.add(informations);
			// for (double d : informations) {
			// logger.info(" imformations_11:"+d);
			// }
			// 求中间一段的积分值
			for (int i = 0; i < tests.size() - 1; i++) {
				// 右边
				tempProbability = Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0)
						* (1.0 / (tests.get(i).coverage + 1.0));
				sumProbability += tempProbability;
				informations = new double[5];
				informations[0] = tempProbability;
				informations[1] = YOUQUXIAN;
				informations[2] = tests.get(i).p;
				informations[3] = (tests.get(i + 1).p + tests.get(i).p) / 2.0;
				informations[4] = tests.get(i).coverage;
				integrals.add(informations);
				// 下一个点的左边
				tempProbability = Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0)
						* (1.0 / (tests.get(i + 1).coverage + 1.0));
				sumProbability += tempProbability;
				informations = new double[5];
				informations[0] = tempProbability;
				informations[1] = ZUOQUXIAN;
				informations[2] = (tests.get(i + 1).p + tests.get(i).p) / 2.0;
				informations[3] = tests.get(i + 1).p;
				informations[4] = tests.get(i + 1).coverage;
				integrals.add(informations);
			}
			tempProbability = Math.pow((max - tests.get(tests.size() - 1).p), 2.0)
					/ (tests.get(tests.size() - 1).coverage + 1.0);
			sumProbability += tempProbability;
			informations = new double[5];
			informations[0] = tempProbability;
			informations[1] = YOUQUXIAN;
			informations[2] = tests.get(tests.size() - 1).p;
			informations[3] = max;
			informations[4] = tests.get(tests.size() - 1).coverage;
			integrals.add(informations);
			// for (double d : informations) {
			//// System.out.println("imformations 22:"+d);
			//// logger.info(" imformations_22:"+d);
			// }
			datum_line = 1.0 / sumProbability;
			double T = random.nextDouble() * 1.0;
			// logger.info("datum_line:"+datum_line);
			// logger.info("T:"+T);
			// 确定在哪一段，然后求解出下一个点
			double SumIntegral = 0.0;
			double PreIntegral = 0.0;
			int temp = 0;
			// 这里有问题，temp的值不一定就是前面一个的temp,理解错了，其实没有错
			for (int i = 0; i < integrals.size(); i++) {
				if (SumIntegral < T) {
					PreIntegral = SumIntegral;
					temp = i;
				}
				SumIntegral += integrals.get(i)[0] * datum_line;
			}
			// draw picture
			// double drawtemp[]=integrals.get(0);
			// DrawCurve draw = new DrawCurve(datum_line, integrals);
			// draw.savePng(count);

			// logger.info("Pre:"+PreIntegral+", temp:"+temp);
			// 求下一个测试用例
			int type = (int) integrals.get(temp)[1];
			double start = integrals.get(temp)[2];
			double end = integrals.get(temp)[3];
			if (type == ZUOQUXIAN) {
				double temp1 = end - start;
				// 这里出错了，这里的p并不是所要的p而是刚才遗留下来的p
				// wrong -> double temp2=p.coverage+1.0
				double temp2 = integrals.get(temp)[4] + 1.0;
				p = new TestCase();
				// this may be wrong
				// double
				// temp3=((datum_line*Math.pow(temp1,2.0)/temp2+PreIntegral-T)*(temp2/(datum_line*temp1)));
				double temp3 = (1.0 - (T - PreIntegral) * (temp2) / ((datum_line) * (Math.pow(temp1, 2.0))));
				// logger.info("temp3:"+temp3);
				p.p = end - temp1 * Math.pow(temp3, (1.0 / temp2));
				// logger.info("p.p:"+p.p);
				// p.coverage = coverageFactory.execute(p.p);// coverageFactory.execute(p.p);
				p.coverage = 10;
			} else {
				double temp1 = end - start;
				// 这里也是错误的->double temp2=p.coverage+1.0
				double temp2 = integrals.get(temp)[4] + 1.0;
				p = new TestCase();
				p.p = start + temp1
						* Math.pow((T - PreIntegral) * temp2 / (datum_line * Math.pow(temp1, 2.0)), (1.0 / temp2));
				// p.coverage = coverageFactory.execute(p.p);// coverageFactory.execute(p.p);
				p.coverage = 10;
			}
			if (Double.isNaN(p.p) || p.p < min || p.p > max) {
				logger.error("Interrupt!!");
			}
		}

		return count;
	}

}
