package test.reality.rrttp;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

import datastructure.TD.TestCase;
import tested.bessj;
import util.HilbertCurve2;

public class RRTtpND {
	public static void main(String[] args) {
		bessj test = new bessj();
		int times = 3000;
		long sums = 0;
		int temp = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			long[] a = new long[3];
			RRTtpND nd = new RRTtpND(test.min, test.max, test.Dimension, 1.5, test.getClass(), i * 5);
			temp = nd.run(a);
			sums += temp;
		}
		long endTime = System.currentTimeMillis();
		double fm = sums / (double) times;
		System.out.println(" d:" + test.Dimension + " Fm:" + fm + " time:" + ((endTime - startTime) / (double) times));

	}
	double[] min;// 各维最小值

	double[] max;// 各维最大值
	double AreaS;
	double FailAreaS;
	double EachFailLength;
	int Dimension;
	double R;
	long randomseed;
	Random random;
	Class<?> classes;
	HilbertCurve2 hiblert;

	ArrayList<TestCase> tests = new ArrayList<>();

	public RRTtpND(double min[], double max[], int dimension, double r, Class<?> classes, long randomseed) {
		super();
		this.min = min;
		this.max = max;
		R = r;
		this.classes = classes;
		this.Dimension = dimension;
		this.randomseed = randomseed;
		random = new Random(randomseed);
		AreaS = 1.0;
		for (int i = 0; i < this.Dimension; i++) {
			AreaS *= (max[i] - min[i]);
		}
		// initFailStart();
		hiblert = new HilbertCurve2();
	}

	public boolean isCorrect(TestCase p) {
		boolean lead2Fail = false;
		// double[] nDPoints = hiblert.oneD_2_nD2(p.p, this.Dimension, innertime);
		double[] nDPoints = hiblert.oneD_2_nD3(p.p + "", this.Dimension);
		System.out.println("P:" + p.p);
		System.out.println(Arrays.toString(nDPoints));
		for (int i = 0; i < this.Dimension; i++) {
			// 一维数映射
			nDPoints[i] = (max[i] - min[i]) * nDPoints[i] + min[i];
		}
		try {
			lead2Fail = Boolean.parseBoolean(
					classes.getMethod("isCorrect", double[].class).invoke(classes.newInstance(), nDPoints).toString());
		} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException
				| SecurityException | InstantiationException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		// lead2Fail=false,失效，=true不失效
		return lead2Fail;
	}

	public TestCase randomTC() {
		TestCase temp = new TestCase();
		double temp_value = random.nextDouble();// 0-1
		temp.p = temp_value;
		return temp;
	}

	public int run(long[] a) {
		int count = 0;
		TestCase p = randomTC();
		boolean isCorrect = isCorrect(p);
		while (isCorrect) {
			count++;
			if (tests.size() == 0) {
				tests.add(p);
			} else {
				long t1 = System.currentTimeMillis();
				sortTestCases(p);
				long t2 = System.currentTimeMillis();
				a[1] += t2 - t1;
			}
			double radius = R / (2 * tests.size());
			double max = -1;
			double start = 0.0;
			double end = 0.0;
			long t1 = System.currentTimeMillis();
			long size = tests.size();
			double length = 0;
			double tempstart = 0.0;
			double tempend = 0.0;
			for (int i = 0; i <= size; i++) {

				boolean flag = true;
				// System.out.println("i:"+i);
				if (i == 0) {
					double temp = tests.get(0).p;
					if (temp - radius > 0) {
						length = temp - radius - 0;
						tempstart = 0;
						tempend = temp - radius;
					} else {
						flag = false;
					}
				} else if (i == size) {
					double temp = tests.get(i - 1).p;
					if (temp + radius <= 1) {
						length = 1 - (temp + radius);
						tempstart = temp + radius;
						tempend = 1;
					} else {
						flag = false;
					}
				} else {
					double temp1 = tests.get(i).p;
					double temp2 = tests.get(i - 1).p;
					if (temp1 - temp2 > 2 * radius) {
						length = temp1 - radius - (temp2 + radius);
						tempstart = temp2 + radius;
						tempend = temp1 - radius;
					} else {
						flag = false;
					}
				}
				if (flag) {
					if (max < length) {
						max = length;
						start = tempstart;
						end = tempend;
					}
				} else {
					continue;
				}
			}
			long t2 = System.currentTimeMillis();
			a[2] += t2 - t1;
			// System.out.println("start:" + start);
			// System.out.println("end:" + end);
			// 选取下一个测试用例
			p = new TestCase();
			p.p = random.nextDouble() * (end - start) + start;
			// System.out.println(p.toString());
			long[] innertime = new long[3];
			// long begintime=System.currentTimeMillis();
			isCorrect = isCorrect(p);
		}
		return count;
	}

	public void sortTestCases(TestCase p) {
		int low = 0, high = tests.size() - 1, mid = -1;
		while (low <= high) {
			mid = (low + high) / 2;
			if (p.p > tests.get(mid).p) {
				low = mid + 1;
			} else {
				high = mid - 1;
			}
		}
		if (p.p < tests.get(mid).p) {
			mid = mid - 1;
		}
		tests.add(mid + 1, p);
	}
}
