package test.reality.rt;

import java.util.Random;

import Fault_10d.CalGCD;
import Fault_10d.NearestDistance;
import Fault_11d.Select;
import Fault_12d.Tcas;
import Fault_5d.CalDay;
import Fault_6d.Complex;
import Fault_6d.Triangle;
import Fault_8d.Line;
import Fault_8d.TwoLinesPos;
import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import tested.airy;
import tested.el2;
import tested.*;

public class RT_ND {
	
	double[] min;
	double[] max;
	int dimension;
	FailurePattern failPattern;

	Random random;

	public RT_ND(double[] min, double[] max, Random random, FailurePattern pattern) {
		this.min = min;
		this.max = max;

		this.dimension = min.length;

		this.random = random;

		pattern.random = random;
		pattern.genFailurePattern();

		this.failPattern = pattern;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		NPoint p = randomTC();
		//System.out.println(p);   ///
		while (this.failPattern.isCorrect(p)) {
			count++;
			//System.out.println(p);   ///
			p = randomTC();
		}
		return count;
	}
	
	public static void main(String[] args) {
		
		// double failrate=0.005;
		//airy beTested=new airy();
		//erfcc beTested=new erfcc();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//tanh beTested=new tanh();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		//Tcas beTested = new Tcas();
		//Select beTested = new Select();
		//Triangle beTested = new Triangle();
		//Line beTested = new Line();
		CalDay beTested = new CalDay();
		//CalGCD beTested = new CalGCD();
		//Complex beTested = new Complex();
		//TwoLinesPos beTested = new TwoLinesPos();
		//NearestDistance beTested = new NearestDistance();
		
		int times = 3000;
		double[] min = beTested.min;
		double[] max = beTested.max;
		FailurePattern failurePattern = new RealityFailPattern(beTested.getClass().getSimpleName());
		failurePattern.fail_rate = beTested.failureRate;
		failurePattern.min = min;
		failurePattern.max = max;
		failurePattern.dimension = beTested.Dimension;

		int fm = 0;
		long startTime1 = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
//			long startTime = System.currentTimeMillis();
			RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);
			int temp = rt.run();
			fm += temp;
//			long endTime = System.currentTimeMillis();
//			System.out.println(endTime - startTime); 
		}
		long endTime1 = System.currentTimeMillis();

		System.out.println("Fm:" + (fm / (double) times) + " times:" + ((endTime1 - startTime1) / (double) times));
	}
}
