package test.simulations.art_tpp;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.StripPatternIn2D;
import datastructure.failurepattern.impl.PointPatternIn2D;
import test.ART;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_TPP extends ART {

	
	ArrayList<NRectRegion> regionLists = new ArrayList<>();
	ArrayList<NPoint> tests = new ArrayList<>();

	int k = 3;

	public ART_TPP(double[] min, double[] max, Random random, FailurePattern failurePattern, int k) {
		super(min, max, random, failurePattern);
		this.k = k;
	}

	public void addRegionsIn2D(NRectRegion region, NPoint p) {
		double xmin = region.getStart().getXn()[0];
		double ymin = region.getStart().getXn()[1];
		double xmax = region.getEnd().getXn()[0];
		double ymax = region.getEnd().getXn()[1];
		double pp = p.getXn()[0];
		double qq = p.getXn()[1];

		NRectRegion first = new NRectRegion(new NPoint(new double[] { xmin, ymin }),
				new NPoint(new double[] { pp, qq }));
		NRectRegion second = new NRectRegion(new NPoint(new double[] { pp, ymin }),
				new NPoint(new double[] { xmax, qq }));
		NRectRegion third = new NRectRegion(new NPoint(new double[] { pp, qq }),
				new NPoint(new double[] { xmax, ymax }));
		NRectRegion fourth = new NRectRegion(new NPoint(new double[] { xmin, qq }),
				new NPoint(new double[] { pp, ymax }));

		this.regionLists.add(first);
		this.regionLists.add(second);
		this.regionLists.add(third);
		this.regionLists.add(fourth);
	}

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
		// int regions=(int) Math.pow(2, this.dimension);
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double[] pxn = p.getXn();
		List<List<Double>> result1 = splitRegions(start, pxn);
		List<List<Double>> result2 = splitRegions(pxn, end);
		// System.out.println(result1.size());
		if (result1.size() != result2.size()) {
			throw new Exception("result1's size!=result2's size ,split region wrong");
		}
		for (int i = 0; i < result1.size(); i++) {
			List<Double> temp1 = result1.get(i);
			List<Double> temp2 = result2.get(i);
			double[] newStart = new double[temp1.size()];
			double[] newEnd = new double[temp2.size()];
			for (int j = 0; j < temp1.size(); j++) {
				newStart[j] = temp1.get(j);
				newEnd[j] = temp2.get(j);
			}

			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));
			this.regionLists.add(tempRegion);
		}
	}

	public double calTwoPointDistance(NPoint p1, NPoint p2) {
		double[] p1xn = p1.getXn();
		double[] p2xn = p2.getXn();
		double distance = 0.0;
		for (int i = 0; i < p1xn.length; i++) {
			distance += Math.pow((p2xn[i] - p1xn[i]), 2);
		}
		distance = Math.sqrt(distance);
		return distance;
	}

	public int findMaxRegion() {
		double max = -1;
		int index = 0;
		for (int i = 0; i < regionLists.size(); i++) {
			double temp = regionLists.get(i).size();
			if (max < temp) {
				max = temp;
				index = i;
			}
		}
		return index;
	}

	public NPoint generateRandomTC(NRectRegion region) {
		NPoint p = new NPoint();
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double xn[] = new double[dimension];

		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];
		}
		p.setXn(xn);
		return p;
	}

	public NPoint hasPointInRegion(NRectRegion region) {
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		NPoint result = null;
		for (int i = 0; i < tests.size(); i++) {
			double[] p = tests.get(i).getXn();
			boolean isPIn = true;
			for (int j = 0; j < p.length; j++) {
				if (p[j] < start[j] || p[j] > end[j]) {
					isPIn = false;
				}
			}
			if (isPIn) {
				result = tests.get(i);
				break;
			}
		}
		return result;
	}

	public NPoint midPoint(NPoint p1, NPoint p2) {
		double[] p1xn = p1.getXn();
		double[] p2xn = p2.getXn();
		double[] mid = new double[this.dimension];
		for (int i = 0; i < mid.length; i++) {
			mid[i] = 0.5 * (p1xn[i] + p2xn[i]);
		}
		return new NPoint(mid);
	}

	@Override
	public int run() {
		int count = 0;
		regionLists.add(new NRectRegion(new NPoint(min), new NPoint(max)));
		NRectRegion currentRegion = regionLists.get(0);
		NPoint p = generateRandomTC(currentRegion);
		tests.add(p);
//		System.out.println(p);
		count++;
		while (true) {
//			while (count < 1000) {
			int maxIndex = findMaxRegion();
			currentRegion = regionLists.remove(maxIndex);
			// regionLists.remove(maxIndex);
			NPoint alreadyPoint = hasPointInRegion(currentRegion);
			NPoint p1 = null;
			NPoint p2 = null;

			if (alreadyPoint == null) {
				p1 = generateRandomTC(currentRegion);
				tests.add(p1);
				count++;
				if (!this.failPattern.isCorrect(p1)) {
					return count;
				}
//				System.out.println(p1);
				// randomly generate k candidate points in curReg, and store them in SC;
				// SC is the test input candidate set
				// select the point from SC which is the farthest from P1 as the second test
				// input P2;
				NPoint[] SC = new NPoint[k];
				double maxLength = 0.0;

				for (int i = 0; i < k; i++) {
					SC[i] = generateRandomTC(currentRegion);
					double length = calTwoPointDistance(p1, SC[i]);
					if (maxLength < length) {
						maxLength = length;
						p2 = SC[i];
					}
				}

			} else {
				p1 = alreadyPoint;
				NPoint[] SC = new NPoint[k];
				double maxLength = 0.0;

				for (int i = 0; i < k; i++) {
					SC[i] = generateRandomTC(currentRegion);
					double length = calTwoPointDistance(p1, SC[i]);
					if (maxLength < length) {
						maxLength = length;
						p2 = SC[i];
					}
				}
			}
			//
			tests.add(p2);
			count++;
			if (!this.failPattern.isCorrect(p2)) {
				return count;
			}
//			System.out.println(p2);
			// System.out.println("p1:"+p1);
			// System.out.println("p2:"+p2);
			// mid point
			NPoint midPoint = midPoint(p1, p2);

			// addRegions(currentRegion.getStart().getXn(),currentRegion.getEnd().getXn(),
			// p.getXn());
			// addRegionsIn2D(currentRegion, midPoint);
			try {
				addRegionsInND(currentRegion, midPoint);
			} catch (Exception e) {
				e.printStackTrace();
			}
			// System.out.println("----------");
		}
//		 return count;
	}

	public List<List<Double>> splitRegions(double[] start, double[] end) {
		ArrayList<double[]> values = new ArrayList<>();
		for (int i = 0; i < start.length; i++) {
			double[] temp = new double[2];

			temp[0] = start[i];
			temp[1] = end[i];
			values.add(temp);
		}

		ArrayList<List<Double>> result = new ArrayList<>();
		PaiLie.per(values, 0, new ArrayList<>(), result);
		return result;
	}

	public static void main(String[] args) {
		int n = 2;
		double fail_rate = 0.001;
		int k = 3;
		ZeroOneCreator dataCreator = new ZeroOneCreator();
		double[] min = dataCreator.minCreator(n);
		double[] max = dataCreator.maxCreator(n);
		
		FailurePattern failurePattern = new BlockPattern();
		//FailurePattern failurePattern = new StripPatternIn2D();
		//FailurePattern failurePattern = new PointPatternIn2D();
		failurePattern.fail_rate = fail_rate;
		int times = 1;

		int fm = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			
			ART_TPP tpp = new ART_TPP(min, max, new Random(i * 3), failurePattern, k);
			int temp = tpp.run();
			fm += temp;
//			if (i % 500 == 0) {
//				System.out.println(i);
//			}
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Fm: "+ fm / (double) times);
		System.out.println("Time: "+(endTime - startTime) / (double) times);
	}

}
