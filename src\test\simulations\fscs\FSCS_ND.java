package test.simulations.fscs;
/*
 * n维实现,包含1维2维等
 * */

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.PointPatternIn2D;
import datastructure.failurepattern.impl.StripPatternIn2D;
import test.ART;
import util.data.ZeroOneCreator;

public class FSCS_ND extends ART {
	public static void main(String[] args) {
		int times = 5000;
		int d = 7;
		// double fail_rate = 0.001;
		ZeroOneCreator dataCreator = new ZeroOneCreator();

		double[] min = dataCreator.minCreator(d);
		double[] max = dataCreator.maxCreator(d);
		int s = 10;

		double [] frate = {0.01, 0.005, 0.002, 0.001, 0.0005, 0.0002};
		//double[] frate = { 0.0002 };
		for (int f = 0; f < frate.length; f++) {

			// FailurePattern failurePattern = new BlockPattern();
			// FailurePattern failurePattern = new StripPatternIn2D();
			FailurePattern failurePattern = new PointPatternIn2D();
			failurePattern.fail_rate = frate[f];

			int fm = 0;
			long startTime = System.currentTimeMillis();
			for (int i = 0; i < times; i++) {
				FSCS_ND test = new FSCS_ND(min, max, s, failurePattern, new Random(i * 3));
				int temp = test.run();
				fm += temp;
			}
			long endTime = System.currentTimeMillis();
			System.out.println("Fm for " + frate[f] + " failure rate: " + "Fm:" + (fm / (double) times) + " time:"
					+ ((endTime - startTime) / (double) times));
			// System.out.println(fm / (double) times);
			// System.out.println((endTime - startTime) / (double) times);
		}
	}

	int s = 10;// 表示候选集的数量初始值为10

	ArrayList<NPoint> tests = new ArrayList<>();

	public FSCS_ND(double[] min, double[] max, int s, FailurePattern pattern, Random random) {
		super(min, max, random, pattern);
		this.s = s;
	}

	public double calTwoPointDistance(NPoint p1, NPoint p2) {
		double[] p1xn = p1.getXn();
		double[] p2xn = p2.getXn();
		double distance = 0.0;
		for (int i = 0; i < p1xn.length; i++) {
			distance += Math.pow((p2xn[i] - p1xn[i]), 2);
		}
		distance = Math.sqrt(distance);
		return distance;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		NPoint p = randomTC();
		// while (this.failPattern.isCorrect(p)) {
		while (this.failPattern.isCorrect(p)) {
			count++;
			tests.add(p);
			p = new NPoint();
			double maxDistance = -1.0;
			NPoint bestCandidate = null;
			for (int i = 0; i < s; i++) {
				NPoint candidate = randomTC();
				// 计算两个点的距离
				double minDistance = Double.MAX_VALUE;

				for (int j = 0; j < this.tests.size(); j++) {
					double tempDistance = calTwoPointDistance(candidate, tests.get(j));
					if (tempDistance < minDistance) {
						minDistance = tempDistance;
					}
				}
				if (maxDistance < minDistance) {
					maxDistance = minDistance;
					bestCandidate = candidate;
				}
			}
			p = null;
			p = bestCandidate;
		}
		return count;
	}
}
