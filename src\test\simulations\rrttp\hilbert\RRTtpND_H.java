package test.simulations.rrttp.hilbert;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import util.HilbertCurve2;

/**
 * RRT_TP的n维实现（利用希尔伯特曲线）
 * -Djava.library.path="${workspace_loc}/ART/Resource;${env_var:PATH}"
 */
public class RRTtpND_H {
	public static void main(String[] args) throws Exception {
		// File file = FileUtils.createNewFile("RRTtp_OD");
		// BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new
		// FileOutputStream(file), "UTF-8"));
		double[] theta = { 0.005 };// , 0.002, 0.0015, 0.001, 0.0005, 0.0001
		int[] d = { 2 };
		for (int v = 0; v < d.length; v++) {
			double min[] = MinCreator(d[v]);
			double max[] = MaxCreator(d[v]);
			for (int k = 0; k < theta.length; k++) {

				int times = 3000;
				long sums = 0;
				int temp = 0;
				// long[] a=new long[1];
				// long timewaste = 0;
				// long timewaste1 = 0;
				// long timewaste2 = 0;
				// long timewaste3 = 0;// 记录double to bin时间
				// long timewaste4 = 0;// 记录映射时间
				// long timewaste5 = 0;// bin to double
				// tan0 tested = new tan0();
				long startTime = System.currentTimeMillis();
				for (int i = 0; i < times; i++) {
					long[] a = new long[6];
					RRTtpND_H rrt = new RRTtpND_H(min, max, 0.75, theta[k], i * 3);
					temp = rrt.run(a);
					sums += temp;
					// timewaste += a[0];
					// timewaste1 += a[1];
					// timewaste2 += a[2];
					// timewaste3 += a[3];
					// timewaste4 += a[4];
					// timewaste5 += a[5];//
				}
				long endTime = System.currentTimeMillis();
				double fm = (sums / (double) times);
				// System.out.println(
				// timewaste + "," + timewaste1 + "," + timewaste2 + " " + (timewaste +
				// timewaste1 + timewaste2));
				// System.out.println("inner:" + timewaste3 / Math.pow(10.0, 6) + " " +
				// timewaste4 / Math.pow(10.0, 6)
				// + " " + timewaste5 / Math.pow(10.0, 6));
				System.out.println("theta:" + theta[k] + " d:" + d[v] + " Fm:" + fm + " time:"
						+ ((endTime - startTime) / (double) times));

				// writer.newLine();
			}
		}
		// writer.close();
	}
	public static double[] MaxCreator(int n) {
		double[] max = new double[n];
		for (int i = 0; i < n; i++) {
			max[i] = 1.0;
		}
		return max;
	}
	public static double[] MinCreator(int n) {
		double[] min = new double[n];
		for (int i = 0; i < n; i++) {
			min[i] = 0;
		}
		return min;
	}
	double[] min;
	double[] max;
	double[] fail_start;
	double AreaS;
	double FailAreaS;
	double EachFailLength;
	double fail_rate;
	int Dimension;
	double R;
	long randomseed;

	Random random;

	HilbertCurve2 hiblert;

	ArrayList<TestCase> tests = new ArrayList<>();

	public RRTtpND_H(double[] min, double[] max, double r, double fail_rate, long randomseed) {
		super();
		this.min = min;
		this.max = max;
		R = r;
		this.fail_rate = fail_rate;
		this.Dimension = min.length;
		this.randomseed = randomseed;
		random = new Random(randomseed);
		initFailStart();
		hiblert = new HilbertCurve2();
	}

	public void initFailStart() {
		this.AreaS = 1.0;
		for (int i = 0; i < this.Dimension; i++) {
			this.AreaS *= (max[i] - min[i]);
		}
		this.FailAreaS = this.fail_rate * this.AreaS;

		this.fail_start = new double[this.Dimension];
		this.EachFailLength = Math.pow(this.FailAreaS, 1 / (double) this.Dimension);
		// System.out.println("EachFailArea:"+this.EachFailLength);
		for (int i = 0; i < this.Dimension; i++) {
			fail_start[i] = random.nextDouble() * (max[i] - min[i] - EachFailLength) + min[i];
		}
	}

	public boolean isCorrect(TestCase p, long[] innertime) {
		// p是一个一维的数
		boolean lead2Fail = false;
		// double[] nDPoints = hiblert.oneD_2_nD2(p.p, this.Dimension, innertime);
		double[] nDPoints = hiblert.oneD_2_nD3(p.p + "", this.Dimension);
		for (int i = 0; i < this.Dimension; i++) {
			if (nDPoints[i] < this.fail_start[i] || nDPoints[i] > (this.fail_start[i] + EachFailLength)) {
				lead2Fail = true;
			}
		}
		// lead2Fail=false,失效，=true不失效
		return lead2Fail;
	}

	public void printFailRegion() {
		for (int i = 0; i < this.Dimension; i++) {
			System.out.println("(" + fail_start[i] + "," + (fail_start[i] + EachFailLength) + ")");
		}
	}

	public TestCase randomTC(Random random) {

		TestCase temp = new TestCase();
		double temp_value = random.nextDouble() * (max[0] - min[0]) + min[0];
		temp.p = temp_value;
		return temp;
	}

	public int run(long[] a) throws Exception {
		int count = 0;

		TestCase p = randomTC(random);// 一维的测试用例
		// System.out.println(p.toString());
		boolean isCorrect = isCorrect(p, new long[3]);
		while (isCorrect) {
			// while(count<30000){
			count++;
			if (tests.size() == 0) {
				tests.add(p);
			} else {
				long t1 = System.currentTimeMillis();
				sortTestCases(p);
				long t2 = System.currentTimeMillis();
				a[1] += t2 - t1;
			}
			double radius = R / (2 * tests.size());
			double max = -1;
			double start = 0.0;
			double end = 0.0;
			long t1 = System.currentTimeMillis();
			long size = tests.size();
			double length = 0;
			double tempstart = 0.0;
			double tempend = 0.0;
			for (int i = 0; i <= size; i++) {

				boolean flag = true;
				// System.out.println("i:"+i);
				if (i == 0) {
					double temp = tests.get(0).p;
					if (temp - radius > this.min[0]) {
						length = temp - radius - min[0];
						tempstart = min[0];
						tempend = temp - radius;
					} else {
						flag = false;
					}
				} else if (i == size) {
					double temp = tests.get(i - 1).p;
					if (temp + radius <= this.max[0]) {
						length = this.max[0] - (temp + radius);
						tempstart = temp + radius;
						tempend = this.max[0];
					} else {
						flag = false;
					}
				} else {
					double temp1 = tests.get(i).p;
					double temp2 = tests.get(i - 1).p;
					if (temp1 - temp2 > 2 * radius) {
						length = temp1 - radius - (temp2 + radius);
						tempstart = temp2 + radius;
						tempend = temp1 - radius;
					} else {
						flag = false;
					}
				}
				if (flag) {
					if (max < length) {
						max = length;
						start = tempstart;
						end = tempend;
					}
				} else {
					continue;
				}
			}
			long t2 = System.currentTimeMillis();
			a[2] += t2 - t1;
			// System.out.println("start:" + start);
			// System.out.println("end:" + end);
			// 选取下一个测试用例
			p = new TestCase();
			p.p = random.nextDouble() * (end - start) + start;
			// System.out.println(p.toString());
			long[] innertime = new long[3];
			// long begintime=System.currentTimeMillis();
			isCorrect = isCorrect(p, innertime);
			// long endtime=System.currentTimeMillis();
			// a[0]+=endtime-begintime;
			// a[3]+=innertime[0];
			// a[4]+=innertime[1];
			// a[5]+=innertime[2];

		}
		// System.out.println("count:"+count);
		return count;
	}

	public void sortTestCases(TestCase p) {
		int low = 0, high = tests.size() - 1, mid = -1;
		while (low <= high) {
			mid = (low + high) / 2;
			if (p.p > tests.get(mid).p) {
				low = mid + 1;
			} else {
				high = mid - 1;
			}
		}
		if (p.p < tests.get(mid).p) {
			mid = mid - 1;
		}
		tests.add(mid + 1, p);
	}
}
