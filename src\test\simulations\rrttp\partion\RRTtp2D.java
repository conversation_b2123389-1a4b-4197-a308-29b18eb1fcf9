package test.simulations.rrttp.partion;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import test.simulations.rrt.RRT_ND;

/**
 * <AUTHOR>
 * @date 2017/11/26
 */
public class RRTtp2D extends RRT_ND {
	public static void main(String[] args) {

	}
	// public double R;
	List<NPoint> tests = new ArrayList<>((int) (1 / this.failPattern.fail_rate));

	List<NRectRegion> regions = new ArrayList<>();

	public RRTtp2D(double[] min, double[] max, Random random, FailurePattern failurePattern, double r) {
		super(min, max, failurePattern, random, r);
		// this.R=r;
	}

	public void calAvailableRegions(NRectRegion currentRegion, NPoint p, double radius) {
		// cut the currentRegion into 8 regions
		// only for 2 dimesion now
		calAvailableRegionsIn2D(currentRegion, p, radius);

	}

	public void calAvailableRegionsIn2D(NRectRegion currentRegion, NPoint p, double radius) {
		double[] start = currentRegion.getStart().getXn();
		double[] end = currentRegion.getEnd().getXn();
		double[] pxn = p.getXn();

		// 1st part(contains two small parts)//
		double xlength = pxn[0] - start[0];
		double ylength = pxn[1] - start[1];
		if (ylength > radius) {
			// 有下半部分
			NRectRegion region1 = new NRectRegion(new NPoint(new double[] { start[0], start[1] }),
					new NPoint(new double[] { pxn[0], pxn[1] - radius }));
			this.regions.add(region1);
		}
		if (xlength > radius) {
			// 有左半部分
			NRectRegion region2 = new NRectRegion(
					new NPoint(new double[] { start[0], pxn[1] - radius > start[1] ? pxn[1] - radius : start[1] }),
					new NPoint(new double[] { pxn[0] - radius, pxn[1] }));
			this.regions.add(region2);
		}

		// 2st part
		// xlength=
	}

	public NPoint genNextPoint() {
		double T = random.nextDouble();
		double allAvailableRegionsSize = 0.0;
		double Co = 0;
		for (int i = 0; i < regions.size(); i++) {
			allAvailableRegionsSize += regions.get(i).size();
		}
		Co = 1.0 / allAvailableRegionsSize;
		double PreIntegral = 0.0;
		double SumIntegral = 0.0;// 积分值总和
		int temp = 0;// 落在哪个区间
		for (int i = 0; i < regions.size(); i++) {
			if (SumIntegral < T) {
				PreIntegral = SumIntegral;
				temp = i;
			}
			SumIntegral += Co * regions.get(i).size();
		}
		//
		NRectRegion currentRegion = regions.remove(temp);
		NPoint p = randomCreator.randomPoint(currentRegion);

		calAvailableRegions(currentRegion, p, calculateRadius(this.tests.size()));

		return p;

	}

	@Override
	public int run() {
		int count = 0;
		// random a point
		NPoint p = randomCreator.randomPoint();
		double radius = calculateRadius(1);
		calAvailableRegions(new NRectRegion(new NPoint(min), new NPoint(max)), p, radius);
		if (this.failPattern.isCorrect(p)) {
			tests.add(p);
			count++;

			p = genNextPoint();
		}
		return count;
	}
}
