package test.simulations.rt;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import util.data.ZeroOneCreator;

public class RT_ND {
	public static void main(String[] args) {
		int times = 5000;
		double failrate = 0.01;
		int d = 10;
		ZeroOneCreator dataCreator = new ZeroOneCreator();
		double[] min = dataCreator.minCreator(d);
		double[] max = dataCreator.maxCreator(d);
		FailurePattern failurePattern = new BlockPattern();
		failurePattern.fail_rate = failrate;
		failurePattern.min = min;
		failurePattern.max = max;
		failurePattern.dimension = d;

		int fm = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);
			int temp = rt.run();
			fm += temp;
		}
		long endTime = System.currentTimeMillis();

		System.out.println("Fm:" + (fm / (double) times) + " times:" + ((endTime - startTime) / (double) times));
	}
	double[] min;
	double[] max;
	int dimension;
	FailurePattern failPattern;

	Random random;

	public RT_ND(double[] min, double[] max, Random random, FailurePattern pattern) {
		this.min = min;
		this.max = max;

		this.dimension = min.length;

		this.random = random;

		pattern.random = random;
		pattern.genFailurePattern();

		this.failPattern = pattern;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		NPoint p = randomTC();
		while (this.failPattern.isCorrect(p)) {
			count++;
			p = randomTC();
		}
		return count;
	}
}
