package tested;

import util.TestProgram;

public class bessj {

	/*
	 * double ACC= 40.0; double BIGNO= 1.0e10; double BIGNI =1.0e-10;
	 * 
	 * ////////////////////////////////////////////////////////////////////// //
	 * Construction/Destruction
	 * //////////////////////////////////////////////////////////////////////
	 * 
	 * public double fabs(double x){ return Math.abs(x); } public double sqrt(double
	 * x){ return Math.sqrt(x); } public double cos(double x){ return Math.cos(x); }
	 * public double sin(double x){ return Math.sin(x); } double bessj0(double x) {
	 * double ax,z; double xx,y,ans,ans1,ans2;
	 * 
	 * if ((ax=fabs(x)) < 8.0) { y=x*x;
	 * ans1=57568490574.0+y*(-13362590354.0+y*(651619640.7
	 * +y*(-11214424.18+y*(77392.33017+y*(-184.9052456)))));
	 * ans2=57568490411.0+y*(1029532985.0+y*(9494680.718
	 * +y*(59272.64853+y*(267.8532712+y*1.0)))); ans=ans1/ans2; } else { z=8.0/ax;
	 * y=z*z; xx=ax-0.785398164; ans1=1.0+y*(-0.1098628627e-2+y*(0.2734510407e-4
	 * +y*(-0.2073370639e-5+y*0.2093887211e-6))); ans2 =
	 * -0.1562499995e-1+y*(0.1430488765e-3 +y*(-0.6911147651e-5+y*(0.7621095161e-6
	 * -y*0.934935152e-7))); ans=sqrt(0.636619772/ax)*(cos(xx)*ans1-z*sin(xx)*ans2);
	 * } return ans; }
	 * 
	 * double bessj0m(double x) { double ax,z; double xx,y,ans,ans1,ans2;
	 * 
	 * ERROR if ((ax=fabs(x)) < 8.0) { if ((ax=fabs(x)) <= 80.0) { y=x*x;
	 * ans1=57568490574.0+y*(-13362590354.0+y*(651619640.7
	 * +y*(-11214424.18+y*(77392.33017+y*(-184.9052456)))));
	 * ans2=57568490411.0+y*(1029532985.0+y*(9494680.718 ERROR
	 * +y*(59272.64853+y*(267.8532712+y*1.0))));
	 * +y*(59272.64853+y*(267.8532712+y+1.0)))); ans=ans1/ans2; } else { z=8.0/ax;
	 * y=z*z; xx=ax-0.785398164; ans1=1.0+y*(-0.1098628627e-2+y*(0.2734510407e-4
	 * +y*(-0.2073370639e-5+y*0.2093887211e-6))); ans2 =
	 * -0.1562499995e-1+y*(0.1430488765e-3 +y*(-0.6911147651e-5+y*(0.7621095161e-6
	 * ERROR -y*0.934935152e-7))); +y*0.934935152e-7)));
	 * ans=sqrt(0.636619772/ax)*(cos(xx)*ans1-z*sin(xx)*ans2); } return ans; }
	 * 
	 * double bessj1(double x) { double ax,z; double xx,y,ans,ans1,ans2;
	 * 
	 * if ((ax=fabs(x)) < 8.0) { y=x*x;
	 * ans1=x*(72362614232.0+y*(-7895059235.0+y*(242396853.1
	 * +y*(-2972611.439+y*(15704.48260+y*(-30.16036606))))));
	 * ans2=144725228442.0+y*(2300535178.0+y*(18583304.74
	 * +y*(99447.43394+y*(376.9991397+y*1.0)))); ans=ans1/ans2; } else { z=8.0/ax;
	 * y=z*z; xx=ax-2.356194491; ans1=1.0+y*(0.183105e-2+y*(-0.3516396496e-4
	 * +y*(0.2457520174e-5+y*(-0.240337019e-6))));
	 * ans2=0.04687499995+y*(-0.2002690873e-3 +y*(0.8449199096e-5+y*(-0.88228987e-6
	 * +y*0.105787412e-6))); ans=sqrt(0.636619772/ax)*(cos(xx)*ans1-z*sin(xx)*ans2);
	 * if (x < 0.0) ans = -ans; } return ans; }
	 * 
	 * double bessj(int n, double x) { int j,jsum,m; double
	 * ax,bj,bjm,bjp,sum,tox,ans;
	 * 
	 * if (n < 2) System.out.println("Index n less than 2 in BESSJ"); ax=fabs(x); if
	 * (ax == 0.0) return 0.0; else if (ax > (float) n) { tox=2.0/ax;
	 * bjm=bessj0(ax); bj=bessj1(ax); for (j=1;j<n;j++) { bjp=j*tox*bj-bjm; bjm=bj;
	 * bj=bjp; } ans=bj; } else { tox=2.0/ax; m=2*((n+(int) sqrt(ACC*n))/2); jsum=0;
	 * bjp=ans=sum=0.0; bj=1.0; for (j=m;j>0;j--) { bjm=j*tox*bj-bjp; bjp=bj;
	 * bj=bjm; if (fabs(bj) > BIGNO) { bj *= BIGNI; bjp *= BIGNI; ans *= BIGNI; sum
	 * *= BIGNI; } if (jsum!=0) sum += bj; jsum= jsum == 0 ? 1 : 0; if (j == n)
	 * ans=bjp; } sum=2.0*sum-bj; ans /= sum; } return x < 0.0 && n%2 == 1 ? -ans :
	 * ans; }
	 * 
	 * double bessjm(int n, double x) { int j,jsum,m; double
	 * ax,bj,bjm,bjp,sum,tox,ans;
	 * 
	 * if (n < 2) System.out.println("Index n less than 2 in BESSJ"); ax=fabs(x); if
	 * (ax == 0.0) return 0.0; else if (ax > (float) n) { tox=2.0/ax; ERROR
	 * bjm=bessj0(ax); bjm=bessj0m(ax); bj=bessj1(ax); for (j=1;j<n;j++) {
	 * bjp=j*tox*bj-bjm; bjm=bj; bj=bjp; } ans=bj; } else { tox=2.0/ax;
	 * m=2*((n+(int) sqrt(ACC*n))/2); jsum=0; bjp=ans=sum=0.0; bj=1.0; for
	 * (j=m;j>0;j--) { bjm=j*tox*bj-bjp; bjp=bj; bj=bjm; if (fabs(bj) > BIGNO) { bj
	 * *= BIGNI; bjp *= BIGNI; ans *= BIGNI; sum *= BIGNI; } if (jsum!=0) sum += bj;
	 * jsum= jsum == 0 ? 1 : 0; if (j == n) ans=bjp; } sum=2.0*sum-bj; ans /= sum; }
	 * return x < 0.0 && n%2 == 1 ? -ans : ans; }
	 * 
	 * double original_fn(double x, double y) { return bessj((int) x, y); }
	 * 
	 * double modified_fn(double x, double y) { return bessjm((int) x, y); }
	 */
	public static void main(String[] args) {
		System.out.println(1 / .001298);
	}
	public double[] min = { 2, -1000 };
	public double[] max = { 300.0, 15000 };
	public double failureRate = 0.001298;

	public int Dimension = 2;

	public boolean isCorrect(double x, double y) {
		// System.out.println("correct:"+correct(x));
		// System.out.println("wrong:"+wrong(x));
		return TestProgram.test_bessj(x, y);
	}

	public boolean isCorrect(double[] x) {
		// System.out.println("correct:"+correct(x));
		// System.out.println("wrong:"+wrong(x));
		if (x.length == this.Dimension) {
			return TestProgram.test_bessj(x[0], x[1]);
		} else {
			return true;
		}
	}
}
