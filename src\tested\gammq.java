package tested;

import util.TestProgram;

public class gammq {

	public double[] min = { 0.0, 0.0 };
	public double[] max = { 1700.0, 40.0 };
	public double failureRate = 0.000690;
	public int Dimension = 2;

	public boolean isCorrect(double x, double y) {
		return TestProgram.test_gammq(x, y);
	}

	/*
	 * double ITMAX = 100; double EPS = 3.0e-7;
	 * 
	 * ////////////////////////////////////////////////////////////////////// //
	 * Construction/Destruction
	 * //////////////////////////////////////////////////////////////////////
	 * 
	 * public double fabs(double x){ return Math.abs(x); } public double sqrt(double
	 * x){ return Math.sqrt(x); } public double cos(double x){ return Math.cos(x); }
	 * public double sin(double x){ return Math.sin(x); } public double log(double
	 * x){ return Math.log(x); } public double exp(double x){ return Math.exp(x); }
	 * 
	 * 
	 * 
	 * double gammln(double xx) { double x,tmp,ser; double[]
	 * cof={76.18009173,-86.50532033,24.01409822,
	 * -1.231739516,0.120858003e-2,-0.536382e-5}; int j; x=xx-1.0; tmp=x+5.5; tmp -=
	 * (x+0.5)*log(tmp); ser=1.0; for (j=0;j<=5;j++) { x += 1.0; ser += cof[j]/x; }
	 * return -tmp+log(2.50662827465*ser); }
	 * 
	 * 
	 * 
	 * void gcf(double gammcf, double a, double x, double gln) { int n; double
	 * gold=0.0,g,fac=1.0,b1=1.0; double b0=0.0,anf,ana,an,a1,a0=1.0;
	 * 
	 * gln=gammln(a); a1=x; for (n=1;n<=ITMAX;n++) { an=(double) n; ana=an-a;
	 * a0=(a1+a0*ana)*fac; b0=(b1+b0*ana)*fac; anf=an*fac; a1=x*a0+anf*a1;
	 * b1=x*b0+anf*b1; if (a1!=0) { fac=1.0/a1; g=b1*fac; if (fabs((g-gold)/g) <
	 * EPS) { gammcf=exp(-x+a*log(x)-(gln))*g; return; } gold=g; } }
	 * System.out.println("a too large, ITMAX too small in routine GCF"); }
	 * 
	 * 
	 * void gser(double gamser, double a, double x, double gln) { int n; double
	 * sum,del,ap;
	 * 
	 * gln=gammln(a); if (x <= 0.0) { if (x < 0.0)
	 * System.out.println("x less than 0 in routine GSER"); gamser=0.0; return; }
	 * else { ap=a; del=sum=1.0/a; for (n=1;n<=ITMAX;n++) { ap += 1.0; del *= x/ap;
	 * sum += del; if (fabs(del) < fabs(sum)*EPS) {
	 * gamser=sum*exp(-x+a*log(x)-(gln)); return; } }
	 * System.out.println("a too large, ITMAX too small in routine GSER"); return; }
	 * }
	 * 
	 * void gserm(double gamser, double a, double x, double gln) { int n; double
	 * sum,del,ap;
	 * 
	 * gln=gammln(a); if (x <= 0.0) { if (x < 0.0)
	 * System.out.println("x less than 0 in routine GSER"); gamser=0.0; return; }
	 * else { ap=a; del=sum=1.0/a; for (n=1;n<=ITMAX;n++) { ap += 1.0; del *= x/ap;
	 * sum += del; ERROR if (fabs(del) < fabs(sum)*EPS) { if (fabs(del) <=
	 * fabs(sum)*EPS) { gamser=sum*exp(-x+a*log(x)-(gln)); return; } }
	 * System.out.println("a too large, ITMAX too small in routine GSER"); return; }
	 * }
	 * 
	 * 
	 * 
	 * double gammq(double a, double x) { double gamser=0.0,gammcf=0.0,gln=0.0;
	 * 
	 * if (x < 0.0 || a <= 0.0)
	 * System.out.println("Invalid arguments in routine GAMMQ"); if (x < (a+1.0)) {
	 * gser(gamser,a,x,gln); return 1.0-gamser; } else { gcf(gammcf,a,x,gln); return
	 * gammcf; } }
	 * 
	 * double gammqm(double a, double x) { double gamser=0.0,gammcf=0.0,gln=0.0;
	 * 
	 * ERROR if (x < 0.0 || a <= 0.0) nrerror("Invalid arguments in routine GAMMQ");
	 * if (x <= 0.0 || a <= 0.0)
	 * System.out.println("Invalid arguments in routine GAMMQ"); ERROR if (x <
	 * (a+1.0)) { if (x <= (a+2.5)) { if (x <= (a+10.0)) { gserm(gamser,a,x,gln);
	 * return 1.0-gamser; } else { gcf(gammcf,a,x,gln); return gammcf; } }
	 * 
	 * 
	 * 
	 * 
	 * 
	 * 
	 * double original_fn(double x, double y) { return gammq((float) x, (float) y);
	 * }
	 * 
	 * double modified_fn(double x, double y) { return gammqm((float) x, (float) y);
	 * }
	 * 
	 * // Need to override this here to get around the nerror problem public boolean
	 * isCorrect(double x, double y) {
	 * 
	 * if (y == 0.0) // nerror problem in modified version return false; else if (x
	 * == 0.0) // nerror problem, but not an error! return true; else // shouldn't
	 * be any nerror problems return original_fn(x, y) == modified_fn(x, y);
	 * 
	 * }
	 */

}
