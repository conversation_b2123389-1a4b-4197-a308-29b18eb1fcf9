package util;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;

public class RandomCreator {
	private Random random;
	private int dimension;
	private double[] min;
	private double[] max;

	public RandomCreator(Random random, int dimension, double[] min, double[] max) {
		this.random = random;
		this.dimension = dimension;
		this.min = min;
		this.max = max;
	}

	public NPoint randomPoint() {
		// generate from the input domain
		NPoint point = new NPoint();
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setDimension(this.dimension);
		point.setXn(xn);
		return point;
	}

	public NPoint randomPoint(NRectRegion region) {
		NPoint p = new NPoint();
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();
		double xn[] = new double[dimension];

		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];
		}
		p.setXn(xn);
		return p;
	}
}
