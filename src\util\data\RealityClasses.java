package util.data;

import tested.airy;
import tested.bessj;
import tested.bessj0;
import tested.cel;
import tested.el2;
import tested.erfcc;
import tested.gammq;
import tested.golden;
import tested.plgndr;
import tested.probks;
import tested.sncndn;
import tested.tanh;

public class RealityClasses {
	public static Class<?>[] get(){
		Class<?>[] classes={airy.class,erfcc.class,probks.class,bessj0.class,tanh.class,bessj.class,gammq.class,sncndn.class,golden.class,plgndr.class,cel.class,el2.class};
		//Class<?>[] classes={sncndn.class,golden.class,plgndr.class,cel.class,el2.class};
		//Class<?>[] classes={airy.class,erfcc.class,probks.class,bessj0.class,tanh.class};
		 //Class<?>[] classes={bessj.class,gammq.class,sncndn.class};
		 //Class<?>[] classes={golden.class,plgndr.class};
		 //Class<?>[] classes={airy.class};
		 //Class<?>[] classes={erfcc.class};
		 //Class<?>[] classes={probks.class};
		 //Class<?>[] classes={bessj0.class};
		 //Class<?>[] classes={tanh.class};
		 //Class<?>[] classes={bessj.class};
		 //Class<?>[] classes={gammq.class};
		 //Class<?>[] classes={sncndn.class};
		 //Class<?>[] classes={golden.class};
		 //Class<?>[] classes={plgndr.class};
		 //Class<?>[] classes={cel.class};
		 //Class<?>[] classes={el2.class};
		return classes;
	}
//	public static double[] getDoubles(Class classes,String colum){
//		try {
//			return (double[])(classes.getDeclaredField(colum).get(null));
//		} catch (Exception e) {
//			System.out.println("error in RealityClasses");
//			e.printStackTrace();
//		}finally {
//			return null;
//		} 
//		
//	}
//	public static int getInt(Class classes,String colum){
//		try {
//			return (int)(classes.getDeclaredField(colum).get(null));
//		} catch (Exception e) {
//			System.out.println("error in RealityClasses");
//			e.printStackTrace();
//		}finally {
//			return 0;
//		} 
//	}
//	public static double getDouble(Class classes,String colum){
//		try {
//			return (double)(classes.getDeclaredField(colum).get(null));
//		} catch (Exception e) {
//			System.out.println("error in RealityClasses");
//			e.printStackTrace();
//		}finally {
//			return 0.0;
//		} 
//	}
}
