/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class util_TestProgram */

#ifndef _Included_util_TestProgram
#define _Included_util_TestProgram
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     util_TestProgram
 * Method:    test_airy
 * Signature: (D)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1airy
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_bessj
 * Signature: (DD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1bessj
  (JNIEnv *, jclass, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_bessj0
 * Signature: (D)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1bessj0
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_cel
 * Signature: (DDDD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1cel
  (JNIEnv *, jclass, jdouble, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_el2
 * Signature: (DDDD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1el2
  (JNIEnv *, jclass, jdouble, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_erfcc
 * Signature: (D)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1erfcc
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_gammq
 * Signature: (DD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1gammq
  (JNIEnv *, jclass, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_golden
 * Signature: (DDD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1golden
  (JNIEnv *, jclass, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_plgndr
 * Signature: (DDD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1plgndr
  (JNIEnv *, jclass, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_probks
 * Signature: (D)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1probks
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_sncndn
 * Signature: (DD)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1sncndn
  (JNIEnv *, jclass, jdouble, jdouble);

/*
 * Class:     util_TestProgram
 * Method:    test_tanh
 * Signature: (D)Z
 */
JNIEXPORT jboolean JNICALL Java_util_TestProgram_test_1tanh
  (JNIEnv *, jclass, jdouble);

#ifdef __cplusplus
}
#endif
#endif
